import { useState, useEffect, useRef, useCallback } from 'react'

import { But<PERSON> } from '@/components/ui/button'
import { CypherEditor } from '@/components/ui/cypher-editor'
import { TagFilter } from '@/components/ui/tag-filter'
import { GraphVisualization } from '@/components/graph/GraphVisualization'
import { graphApi, neo4jApi } from '@/services/api'
import { colorManager } from '@/lib/colorManager'
import { ensureInitialized } from '@/services/globalInitService'
import { useToast } from '@/components/ui/toast'
import '@/lib/colorUtils' // 引入颜色调试工具
import {
  RefreshCw,
  Loader2,
  Filter,
  ChevronDown,
  ChevronUp
} from 'lucide-react'

interface GraphNode {
  id: string
  label: string
  type: string
  labels?: string[] // 保存原始标签数组
  properties: Record<string, any>
}

interface GraphEdge {
  id: string
  source: string
  target: string
  label: string
  type: string
  properties: Record<string, any>
  hasOriginalId?: boolean // 标记是否有原始ID，用于判断是否显示ElementId
}

interface GraphData {
  nodes: GraphNode[]
  edges: GraphEdge[]
}

interface NodeTypeInfo {
  label: string
  count: number
  color: string
}

interface RelationshipTypeInfo {
  type: string
  count: number
}

// 选中元素的类型定义
interface SelectedElement {
  type: 'node' | 'edge'
  data: GraphNode | GraphEdge
}

export function GraphAnalysis() {
  const [graphData, setGraphData] = useState<GraphData>({ nodes: [], edges: [] })
  const [loading, setLoading] = useState(false)
  const [cypherQuery, setCypherQuery] = useState('MATCH (n)-[r]-(m) RETURN n, r, m, labels(n) as n_labels, labels(m) as m_labels LIMIT 25')
  const [hoveredElement, setHoveredElement] = useState<SelectedElement | null>(null)
  const [selectedElement, setSelectedElement] = useState<SelectedElement | null>(null)
  const [, setOverview] = useState<any>(null)
  const [nodeTypes, setNodeTypes] = useState<NodeTypeInfo[]>([])
  const [relationshipTypes, setRelationshipTypes] = useState<RelationshipTypeInfo[]>([])
  const [queryHistory, setQueryHistory] = useState<string[]>([])

  // 过滤器状态
  const [selectedNodeLabels, setSelectedNodeLabels] = useState<string[]>(['all'])
  const [selectedRelationshipTypes, setSelectedRelationshipTypes] = useState<string[]>(['all'])
  const [showFilters, setShowFilters] = useState(false)

  // 使用ref来防止重复请求
  const hasInitialized = useRef(false)
  const isLoadingOverview = useRef(false)

  // Toast 功能
  const { showToast } = useToast()

  // 通用查询模板 - 不依赖特定标签
  // const queryTemplates = [
  //   { name: '基础关系', query: 'MATCH (n)-[r]-(m) RETURN n, r, m LIMIT 25' },
  //   { name: '节点采样', query: 'MATCH (n) RETURN n LIMIT 50' },
  //   { name: '路径探索', query: 'MATCH p = (a)-[*1..2]-(b) RETURN p LIMIT 15' },
  //   { name: '度中心性', query: 'MATCH (n) RETURN n, size((n)--()) AS degree ORDER BY degree DESC LIMIT 20' },
  //   { name: '完整网络', query: 'MATCH (n) OPTIONAL MATCH (n)-[r]-(m) RETURN n, r, m LIMIT 50' }
  // ]

  // 导入全局颜色管理器（放在组件外部）

  // 加载图概览统计
  useEffect(() => {
    if (hasInitialized.current || isLoadingOverview.current) return
    loadOverview()
  }, [])

  const loadOverview = async () => {
    // 防止重复加载
    if (isLoadingOverview.current) return
    isLoadingOverview.current = true

    try {
      // 确保全局初始化完成
      await ensureInitialized()

      // 获取Schema信息（颜色已在全局初始化）
      const schemaData: any = await neo4jApi.getSchema()

      // 处理节点类型信息
      if (schemaData.labels) {
        // 直接基于schema创建节点类型信息，使用已初始化的颜色
        const nodeTypeStats: NodeTypeInfo[] = schemaData.labels.map((label: string) => ({
          label,
          count: 0, // 初始计数为0，后续可以通过其他方式更新
          color: colorManager.getColor(label)
        }))
        setNodeTypes(nodeTypeStats)
      }

      // 获取统计信息（用于更新节点计数等其他数据）
      const data: any = await neo4jApi.getStatistics()
      setOverview(data)

      // 如果统计数据包含节点计数，更新已有的节点类型信息
      if (data.nodes_by_label && schemaData.labels) {
        // 处理节点统计数据（与NodeManagement页面相同的逻辑）
        const nodeCounts: Record<string, number> = {}

        schemaData.labels.forEach((label: string) => {
          let count = 0
          Object.entries(data.nodes_by_label).forEach(([labelKey, labelCount]) => {
            const labels = labelKey.split(':')
            if (labels.includes(label)) {
              count += labelCount as number
            }
          })
          nodeCounts[label] = count
        })

        setNodeTypes(prevTypes =>
          prevTypes.map(nodeType => ({
            ...nodeType,
            count: nodeCounts[nodeType.label] || 0
          }))
        )
      }

      // 处理关系类型统计
      if (data.relationships_by_type) {
        const relTypeStats: RelationshipTypeInfo[] = Object.entries(data.relationships_by_type).map(([type, count]) => ({
          type,
          count: count as number
        }))
        setRelationshipTypes(relTypeStats)
      }

      // 标记初始化完成，然后执行默认查询
      hasInitialized.current = true
      executeCypherQuery()

    } catch (error) {
      console.error('Failed to load overview:', error)
    } finally {
      isLoadingOverview.current = false
    }
  }

  // 处理新的Neo4j API响应格式
  const processNeo4jResponse = (result: any): GraphData => {
    
    if (result.status !== 'completed' || !result.result) {
      console.warn('查询未完成或无结果:', result)
      return { nodes: [], edges: [] }
    }

    const { nodes: rawNodesData = {}, relationships: rawRelationships = [] } = result.result

    // 处理节点数据 - 新格式中节点分别在不同的变量中
    const allRawNodes: any[] = []

    // 收集所有节点数据
    Object.values(rawNodesData).forEach((nodeArray: any) => {
      if (Array.isArray(nodeArray)) {
        allRawNodes.push(...nodeArray)
      }
    })

    // 去重节点（基于identity）
    const uniqueNodesMap = new Map()
    allRawNodes.forEach(node => {
      const nodeId = node.properties?.business_id || node.identity || node.id || Math.random().toString()
      if (!uniqueNodesMap.has(nodeId)) {
        uniqueNodesMap.set(nodeId, node)
      }
    })

    const nodes: GraphNode[] = Array.from(uniqueNodesMap.values()).map((node: any) => {
      const nodeId = node.properties?.business_id || node.identity || node.id || Math.random().toString()
      // 优先使用标签数组中的最后一个标签来区分节点颜色
      const lastLabel = node.labels && node.labels.length > 0
        ? node.labels[node.labels.length - 1]
        : 'node'

      return {
        id: nodeId.toString(),
        label: node.properties?.name ||
               node.properties?.title ||
               node.properties?.policy_code ||
               node.properties?.code ||
               `节点${nodeId}`,
        type: lastLabel,
        labels: node.labels || [],
        properties: node.properties || {}
      }
    })



    // 创建节点ID映射
    const nodeIds = new Set(nodes.map(n => n.id))
    
    // 处理关系数据
    const edges: GraphEdge[] = rawRelationships
      .map((rel: any) => {
        // 新格式中使用 start 和 end 字段
        const sourceId = rel.start?.toString() || rel.start_node_id?.toString()
        const targetId = rel.end?.toString() || rel.end_node_id?.toString()

        // 检查是否有有效的原始ID
        const hasOriginalId = rel.identity !== undefined && rel.identity !== null && rel.identity !== ''
        const edgeId = hasOriginalId ? rel.identity.toString() : `${sourceId}-${targetId}-${Date.now()}-${Math.floor(Math.random() * 1000)}`

        const processedEdge = {
          id: edgeId,
          source: sourceId,
          target: targetId,
          label: rel.type || '关联',
          type: rel.type || 'RELATED',
          properties: rel.properties || {},
          hasOriginalId // 添加标记，用于判断是否显示ElementId
        }

        return processedEdge
      })
      .filter((edge: GraphEdge) => {
        const hasValidSource = edge.source && nodeIds.has(edge.source)
        const hasValidTarget = edge.target && nodeIds.has(edge.target)
        
        if (!hasValidSource || !hasValidTarget) {
          console.warn('过滤无效边:', {
            edge: edge.id,
            source: edge.source,
            target: edge.target,
            hasValidSource,
            hasValidTarget
          })
          return false
        }
        return true
      })



    return { nodes, edges }
  }

  // 根据过滤器修改Cypher查询
  const applyFiltersToQuery = useCallback((originalQuery: string) => {
    // 如果选择了"全部"，则不需要过滤
    if (selectedNodeLabels.includes('all') && selectedRelationshipTypes.includes('all')) {
      return originalQuery
    }

    let modifiedQuery = originalQuery
    const conditions: string[] = []

    // 处理节点标签过滤
    if (!selectedNodeLabels.includes('all') && selectedNodeLabels.length > 0) {
      // 构建节点标签过滤条件
      const nodeLabelConditions = selectedNodeLabels.map(label => `'${label}' IN labels(n)`).join(' OR ')
      const targetLabelConditions = selectedNodeLabels.map(label => `'${label}' IN labels(m)`).join(' OR ')
      conditions.push(`(${nodeLabelConditions})`)
      conditions.push(`(${targetLabelConditions})`)
    }

    // 处理关系类型过滤
    if (!selectedRelationshipTypes.includes('all') && selectedRelationshipTypes.length > 0) {
      // 构建关系类型过滤条件
      const relationshipConditions = selectedRelationshipTypes.map(type => `type(r) = '${type}'`).join(' OR ')
      conditions.push(`(${relationshipConditions})`)
    }

    // 如果有过滤条件，添加到查询中
    if (conditions.length > 0) {
      const whereClause = conditions.join(' AND ')

      if (modifiedQuery.toUpperCase().includes('WHERE')) {
        // 如果已有WHERE子句，在现有条件前添加过滤条件
        modifiedQuery = modifiedQuery.replace(
          /WHERE\s+/i,
          `WHERE ${whereClause} AND `
        )
      } else {
        // 在RETURN之前添加WHERE子句
        modifiedQuery = modifiedQuery.replace(
          /\s+RETURN/i,
          ` WHERE ${whereClause} RETURN`
        )
      }
    }

    return modifiedQuery
  }, [selectedNodeLabels, selectedRelationshipTypes])

  // 执行Cypher查询
  const executeCypherQuery = useCallback(async () => {
    if (!cypherQuery.trim()) return

    setLoading(true)
    try {
      // 应用过滤器到查询
      const filteredQuery = applyFiltersToQuery(cypherQuery)
      console.log('原始查询:', cypherQuery)
      console.log('过滤后查询:', filteredQuery)

      // 使用新的Neo4j查询接口
      const result: any = await neo4jApi.executeQuery({
        query: filteredQuery,
        response_format: 'reference'
      })

      // 添加到查询历史
      if (!queryHistory.includes(cypherQuery)) {
        setQueryHistory(prev => [cypherQuery, ...prev.slice(0, 4)]) // 保留最近5个查询
      }

      // 处理新的响应格式
      const graphData = processNeo4jResponse(result)

      // 检查查询结果是否包含未知的标签类型（理论上不应该有，因为已经从schema初始化了所有标签）
      const allLabels = new Set<string>()
      graphData.nodes.forEach(node => {
        if (node.labels) {
          node.labels.forEach(label => allLabels.add(label))
        }
        if (node.type) {
          allLabels.add(node.type)
        }
      })

      // 检查是否有未在schema中定义的标签
      const existingColors = colorManager.getAllColors()
      const unknownLabels = Array.from(allLabels).filter(label => !existingColors[label])

      if (unknownLabels.length > 0) {
        colorManager.updateColors({ node_labels: unknownLabels })
      }

      setGraphData(graphData)

    } catch (error) {
      console.error('Failed to execute cypher query:', error)
      setGraphData({ nodes: [], edges: [] })
    } finally {
      setLoading(false)
    }
  }, [cypherQuery, queryHistory, applyFiltersToQuery])

  // 悬浮节点
  const handleNodeHover = useCallback((node: GraphNode) => {
    setHoveredElement({ type: 'node', data: node })
  }, [])

  // 悬浮边
  const handleEdgeHover = useCallback((edge: GraphEdge) => {
    setHoveredElement({ type: 'edge', data: edge })
  }, [])

  // 悬浮空白
  const handleClearHover = useCallback(() => {
    setHoveredElement(null)
  }, [])

  // 点击节点
  const handleNodeSelect = useCallback((node: GraphNode) => {
    setSelectedElement({ type: 'node', data: node })
  }, [])

  // 点击边
  const handleEdgeSelect = useCallback((edge: GraphEdge) => {
    setSelectedElement({ type: 'edge', data: edge })
  }, [])

  // 处理节点双击事件（加载下级节点）
  const handleNodeDoubleClick = useCallback(async (node: GraphNode) => {
    try {
      // 获取所有节点标签，用冒号连接多个标签
      const allLabels = node.labels && node.labels.length > 0 
        ? node.labels.join(':') 
        : (node.type || 'Node')

      // 根据图数据规模动态调整参数
      const currentNodeCount = graphData.nodes.length
      const dynamicLimit = currentNodeCount > 100 ? 10 : currentNodeCount > 50 ? 15 : 20
      const nodeId = node.properties?.business_id || node.id
      const neighbors: any = await graphApi.getNodeNeighbors(nodeId, allLabels, {
        depth: 1,
        limit: dynamicLimit
      })
      if (neighbors?.nodes || neighbors?.result?.nodes) {
        const neighborData = processNeo4jResponse(neighbors)
        // 合并邻居数据到当前图数据
        const existingNodeIds = new Set(graphData.nodes.map(n => n.id))
        const newNodes = neighborData.nodes.filter(n => !existingNodeIds.has(n.id))
        const existingEdgeIds = new Set(graphData.edges.map(e => e.id))
        const newEdges = neighborData.edges.filter(e => !existingEdgeIds.has(e.id))

        // 只有在真正有新数据时才更新状态
        if (newNodes.length > 0 || newEdges.length > 0) {
  

          // 检查新节点是否包含未知的标签类型（理论上不应该有，因为已经从schema初始化了所有标签）
          const allLabels = new Set<string>()
          newNodes.forEach(node => {
            if (node.labels) {
              node.labels.forEach(label => allLabels.add(label))
            }
            if (node.type) {
              allLabels.add(node.type)
            }
          })

          // 检查是否有未在schema中定义的标签
          const existingColors = colorManager.getAllColors()
          const unknownLabels = Array.from(allLabels).filter(label => !existingColors[label])

          if (unknownLabels.length > 0) {
            console.warn('⚠️ 动态加载发现未在schema中定义的标签，这可能表示schema数据不完整:', unknownLabels)
            colorManager.updateColors({ node_labels: unknownLabels })
          }

          setGraphData(prev => ({
            nodes: [...prev.nodes, ...newNodes],
            edges: [...prev.edges, ...newEdges]
          }))
        } else {
          // 显示轻提示
          showToast('没有找到新的关联节点', 'info', 2000)
        }
      } else {
        // 显示轻提示
        showToast('没有找到关联数据', 'info', 2000)
      }
    } catch (error) {
      console.error('Failed to load neighbors:', error)
    }
  }, [graphData.nodes, graphData.edges])

  // 计算显示元素：悬浮优先，然后选中，最后概览
  const displayElement = hoveredElement || selectedElement

  // 注意：初始查询已在 loadOverview 中调用，这里不需要重复执行

  // 当过滤器改变时重新执行查询
  useEffect(() => {
    if (hasInitialized.current && cypherQuery.trim()) {
      executeCypherQuery()
    }
  }, [selectedNodeLabels, selectedRelationshipTypes]) // eslint-disable-line react-hooks/exhaustive-deps

  const handleReset = () => {
    setGraphData({ nodes: [], edges: [] })
    setHoveredElement(null)
    setSelectedElement(null)
    // 重置过滤器
    setSelectedNodeLabels(['all'])
    setSelectedRelationshipTypes(['all'])
    // 注意：不清空 nodeTypes 和 relationshipTypes，因为这些是从 schema 加载的基础数据
    // 不清空 overview，因为这些是统计数据，重置后应该保留
  }

  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* 查询输入区域 */}
      <div className="flex-shrink-0 p-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-stretch space-x-2">
          <div className="flex-1">
            <CypherEditor
              value={cypherQuery}
              onChange={setCypherQuery}
              onExecute={executeCypherQuery}
              height={64}
            />
          </div>
          <Button
            onClick={executeCypherQuery}
            disabled={loading || !cypherQuery.trim()}
            className="h-8 leading-tight"
          >
            {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : '执行'}
          </Button>
          <Button
            onClick={() => setShowFilters(!showFilters)}
            variant="outline"
            className={`h-8 leading-tight relative transition-all duration-200 ${
              showFilters
                ? 'bg-blue-50 border-blue-300 text-blue-700 dark:bg-blue-900/50 dark:border-blue-600 dark:text-blue-200 shadow-md'
                : 'hover:bg-gray-50 dark:hover:bg-gray-800'
            }`}
          >
            <Filter className={`h-4 w-4 mr-1 transition-colors ${
              (!selectedNodeLabels.includes('all') || !selectedRelationshipTypes.includes('all'))
                ? 'text-blue-600 dark:text-blue-400'
                : ''
            }`} />
            过滤
            {/* 过滤状态指示器 */}
            {(!selectedNodeLabels.includes('all') || !selectedRelationshipTypes.includes('all')) && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            )}
            {showFilters ? <ChevronUp className="h-3 w-3 ml-1" /> : <ChevronDown className="h-3 w-3 ml-1" />}
          </Button>
          <Button
            onClick={handleReset}
            variant="outline"
            className="h-8 leading-tight"
          >
            重置
          </Button>
        </div>

        {/* 过滤器区域 - 可展开收起 */}
        {showFilters && (
          <div className="mt-4 space-y-6 border-t border-gray-200 dark:border-gray-700 pt-4 animate-in slide-in-from-top-2 duration-200">
            {/* 过滤状态提示 */}
            <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
              <span>选择要查询的数据类型</span>
              <span className="text-xs">
                {(!selectedNodeLabels.includes('all') || !selectedRelationshipTypes.includes('all'))
                  ? `已应用过滤条件`
                  : '显示全部数据'
                }
              </span>
            </div>
            {/* 节点标签过滤器 */}
            <TagFilter
              title="节点标签过滤"
              options={[
                { value: 'all', label: '全部', count: nodeTypes.reduce((sum, type) => sum + type.count, 0) },
                ...nodeTypes.map(type => ({
                  value: type.label,
                  label: type.label,
                  count: type.count,
                  color: type.color
                }))
              ]}
              selectedValues={selectedNodeLabels}
              onSelectionChange={setSelectedNodeLabels}
            />

            {/* 关系类型过滤器 */}
            <TagFilter
              title="关系类型过滤"
              options={[
                { value: 'all', label: '全部', count: relationshipTypes.reduce((sum, type) => sum + type.count, 0) },
                ...relationshipTypes.map(type => ({
                  value: type.type,
                  label: type.type,
                  count: type.count
                }))
              ]}
              selectedValues={selectedRelationshipTypes}
              onSelectionChange={setSelectedRelationshipTypes}
            />
          </div>
        )}
      </div>

      {/* 图谱可视化区域 - 最大化 */}
      <div className="flex-1 relative overflow-hidden">
        {loading ? (
          <div className="absolute inset-0 flex items-center justify-center bg-white dark:bg-gray-800">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-blue-500" />
              <p className="text-gray-600 dark:text-gray-400">执行查询中...</p>
            </div>
          </div>
        ) : (
          <div className="relative h-full">
            <GraphVisualization
              data={graphData}
              onNodeHover={handleNodeHover}
              onEdgeHover={handleEdgeHover}
              onClearHover={handleClearHover}
              onNodeSelect={handleNodeSelect}
              onEdgeSelect={handleEdgeSelect}
              onNodeDoubleClick={handleNodeDoubleClick}
              hoveredElement={displayElement}
              nodeTypes={nodeTypes}
              relationshipTypes={relationshipTypes}
            />
          </div>
        )}
      </div>
    </div>
  )
} 