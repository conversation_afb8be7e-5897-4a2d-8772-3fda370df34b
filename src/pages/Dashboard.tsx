import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { statsApi, systemApi } from '@/services/api'
import { 
  Users, 
  Building2, 
  FileText, 
  AlertTriangle, 
  RefreshCw,
  Activity,
  Database,
  TrendingUp
} from 'lucide-react'

export function Dashboard() {
  const [stats, setStats] = useState<any>(null)
  const [nodeStats, setNodeStats] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [healthStatus, setHealthStatus] = useState<any>(null)
  
  // 使用ref来防止重复请求
  const hasInitialized = useRef(false)

  // 加载统计数据
  const loadStats = async () => {
    setLoading(true)
    try {
      const [overallStats, nodeStatsData] = await Promise.all([
        statsApi.getOverallStats(),
        statsApi.getNodeStats()
      ])
      
      setStats(overallStats)
      setNodeStats(nodeStatsData)
      setLastUpdated(new Date())
    } catch (error) {
      console.error('Failed to load statistics:', error)
    } finally {
      setLoading(false)
    }
  }

  // 检查系统健康状态
  const checkHealth = async () => {
    try {
      const health = await systemApi.healthCheck()
      setHealthStatus(health)
    } catch (error) {
      console.error('Failed to check health:', error)
      setHealthStatus({ status: 'error', message: '无法连接到后端服务' })
    }
  }

  useEffect(() => {
    if (hasInitialized.current) return
    hasInitialized.current = true
    loadStats()
    checkHealth()
  }, [])

  return (
    <div className="p-3 space-y-3 h-full overflow-auto">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-xl font-bold tracking-tight">仪表板</h1>
          <p className="text-sm text-muted-foreground">
            保险关系图谱系统概览
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={loadStats}
            disabled={loading}
          >
            <RefreshCw className={`h-3 w-3 mr-1 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          {lastUpdated && (
            <p className="text-xs text-muted-foreground">
              最后更新: {lastUpdated.toLocaleTimeString()}
            </p>
          )}
        </div>
      </div>

      {/* 系统健康状态 */}
      {healthStatus && (
        <Card className="py-1">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center space-x-2 text-sm">
              <Activity className="h-4 w-4" />
              <span>系统状态</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                healthStatus.status === 'healthy' || healthStatus.status === 'ok' 
                  ? 'bg-green-500' 
                  : 'bg-red-500'
              }`} />
              <span className="text-xs">
                {healthStatus.status === 'healthy' || healthStatus.status === 'ok' 
                  ? '系统正常运行' 
                  : healthStatus.message || '系统异常'}
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 核心统计卡片 */}
      <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1">
            <CardTitle className="text-xs font-medium">总节点数</CardTitle>
            <Database className="h-3 w-3 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pt-1">
            <div className="text-lg font-bold">
              {stats?.total_nodes?.toLocaleString() || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              图数据库中的节点总数
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1">
            <CardTitle className="text-xs font-medium">总关系数</CardTitle>
            <TrendingUp className="h-3 w-3 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pt-1">
            <div className="text-lg font-bold">
              {stats?.total_relationships?.toLocaleString() || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              节点间的关系连接数
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1">
            <CardTitle className="text-xs font-medium">节点类型</CardTitle>
            <Building2 className="h-3 w-3 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pt-1">
            <div className="text-lg font-bold">
              {stats?.node_labels?.length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              不同的节点标签类型
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1">
            <CardTitle className="text-xs font-medium">关系类型</CardTitle>
            <AlertTriangle className="h-3 w-3 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pt-1">
            <div className="text-lg font-bold">
              {stats?.relationship_types?.length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              不同的关系类型
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 详细节点统计 */}
      {nodeStats && (
        <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
          {/* 人员统计 */}
          {nodeStats.persons && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center space-x-2 text-sm">
                  <Users className="h-4 w-4" />
                  <span>人员节点</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0 space-y-1">
                {Object.entries(nodeStats.persons).map(([type, data]: [string, any]) => (
                  <div key={type} className="flex justify-between items-center">
                    <span className="text-xs capitalize">{type}</span>
                    <span className="font-medium text-sm">{data?.total_count || 0}</span>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* 公司统计 */}
          {nodeStats.companies && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center space-x-2 text-sm">
                  <Building2 className="h-4 w-4" />
                  <span>公司节点</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0 space-y-1">
                {Object.entries(nodeStats.companies).map(([type, data]: [string, any]) => (
                  <div key={type} className="flex justify-between items-center">
                    <span className="text-xs capitalize">{type}</span>
                    <span className="font-medium text-sm">{data?.total_count || 0}</span>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* 政策统计 */}
          {nodeStats.policies && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center space-x-2 text-sm">
                  <FileText className="h-4 w-4" />
                  <span>保单节点</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0 space-y-1">
                {Object.entries(nodeStats.policies).map(([type, data]: [string, any]) => (
                  <div key={type} className="flex justify-between items-center">
                    <span className="text-xs capitalize">{type}</span>
                    <span className="font-medium text-sm">{data?.total_count || 0}</span>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* 节点类型列表 */}
      {stats?.node_labels && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">节点类型详情</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {stats.node_labels.map((label: string, index: number) => (
                <div key={index} className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                  <div className="w-2 h-2 bg-blue-500 rounded-full" />
                  <span className="text-xs font-medium">{label}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 关系类型列表 */}
      {stats?.relationship_types && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">关系类型详情</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {stats.relationship_types.map((type: string, index: number) => (
                <div key={index} className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span className="text-xs font-medium">{type}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 