import { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import {
  Search,
  RefreshCw,
  Eye,
  Database,
  Users,
  Building2,
  FileText,
  Package,
  MapPin,
  Activity,
  TrendingUp,
  Filter,
  Download,
  ChevronRight
} from 'lucide-react'
import { graphApi } from '@/services/api'
import { getNodeColor } from '@/lib/colorManager'
import { ensureInitialized } from '@/services/globalInitService'

// 节点类型定义
interface NodeItem {
  id: string
  label: string
  type: string
  labels: string[]
  properties: Record<string, any>
}

interface NodeTypeStats {
  label: string
  count: number
  color: string
}

export function NodeManagement() {
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTypes, setSelectedTypes] = useState<string[]>(['all'])
  const [searchResults, setSearchResults] = useState<NodeItem[]>([])
  const [nodeTypes, setNodeTypes] = useState<NodeTypeStats[]>([])
  const [totalNodes, setTotalNodes] = useState(0)
  const [activeTab, setActiveTab] = useState('overview')
  const [selectedNode, setSelectedNode] = useState<NodeItem | null>(null)
  const [showNodeDetails, setShowNodeDetails] = useState(false)

  // 获取节点类型图标
  const getNodeIcon = (labels: string[]) => {
    if (labels.some(l => l.includes('Person'))) return <Users className="h-3 w-3" />
    if (labels.some(l => l.includes('Organization') || l.includes('Company'))) return <Building2 className="h-3 w-3" />
    if (labels.some(l => l.includes('Policy'))) return <FileText className="h-3 w-3" />
    if (labels.some(l => l.includes('Product'))) return <Package className="h-3 w-3" />
    if (labels.some(l => l.includes('Area'))) return <MapPin className="h-3 w-3" />
    return <Database className="h-3 w-3" />
  }

  // 处理类型选择
  const handleTypeToggle = (type: string) => {
    if (type === 'all') {
      setSelectedTypes(['all'])
    } else {
      setSelectedTypes(prev => {
        const filtered = prev.filter(t => t !== 'all')
        if (filtered.includes(type)) {
          const newTypes = filtered.filter(t => t !== type)
          return newTypes.length === 0 ? ['all'] : newTypes
        } else {
          return [...filtered, type]
        }
      })
    }
  }

  // 获取选中类型的显示文本
  const getSelectedTypesText = () => {
    if (selectedTypes.includes('all') || selectedTypes.length === 0) {
      return '所有类型'
    }
    if (selectedTypes.length === 1) {
      return selectedTypes[0]
    }
    return `已选择 ${selectedTypes.length} 个类型`
  }

  // 初始化数据
  useEffect(() => {
    loadNodeTypes()
  }, [])

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const dropdown = document.getElementById('type-dropdown')
      const button = event.target as HTMLElement
      if (dropdown && !button.closest('#type-dropdown') && !button.closest('[data-dropdown-trigger]')) {
        dropdown.style.display = 'none'
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // 加载节点类型统计
  const loadNodeTypes = async () => {
    try {
      setLoading(true)

      // 确保全局初始化完成
      await ensureInitialized()

      const schemaData = await graphApi.getNodeLabels()
      const statsData = await graphApi.getOverview()

      if (schemaData.labels && statsData.nodes_by_label) {
        // 处理节点统计数据
        // 统计数据中的key是组合标签（如"InsuredPerson:Person"），需要匹配到单个标签
        const nodeCounts: Record<string, number> = {}

        // 为每个schema标签计算总数
        schemaData.labels.forEach((label: string) => {
          let count = 0

          // 遍历统计数据，找到包含该标签的所有组合
          Object.entries(statsData.nodes_by_label).forEach(([labelKey, labelCount]) => {
            // 检查标签组合是否包含当前标签
            const labels = labelKey.split(':')
            if (labels.includes(label)) {
              count += labelCount as number
            }
          })

          nodeCounts[label] = count
        })

        const nodeTypeStats: NodeTypeStats[] = schemaData.labels.map((label: string) => {
          const color = getNodeColor(label)
          return {
            label,
            count: nodeCounts[label] || 0,
            color: color || '#8993A4' // 确保有备用颜色
          }
        })

        setNodeTypes(nodeTypeStats)
        setTotalNodes(statsData.total_nodes || 0)
      }
    } catch (error) {
      console.error('加载节点类型失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 搜索节点
  const searchNodes = useCallback(async () => {
    if (!searchTerm.trim()) {
      setSearchResults([])
      return
    }

    try {
      setLoading(true)

      // 构建精准匹配的Cypher查询
      let cypherQuery = ''
      const searchValue = searchTerm.trim()

      if (selectedTypes.includes('all') || selectedTypes.length === 0) {
        // 搜索所有类型
        cypherQuery = `
          MATCH (n)
          WHERE n.code = '${searchValue}' OR n.name = '${searchValue}'
          RETURN n, labels(n) as n_labels
          LIMIT 50
        `
      } else {
        // 搜索指定类型
        const labelConditions = selectedTypes.map(type => `'${type}' IN labels(n)`).join(' OR ')
        cypherQuery = `
          MATCH (n)
          WHERE (${labelConditions}) AND (n.code = '${searchValue}' OR n.name = '${searchValue}')
          RETURN n, labels(n) as n_labels
          LIMIT 50
        `
      }

      const result = await graphApi.executeCypher({
        query: cypherQuery
      })

      if (result.result?.nodes?.n) {
        const nodes: NodeItem[] = result.result.nodes.n.map((node: any) => {
          return {
            id: node.properties?.business_id || node.identity || Math.random().toString(),
            label: node.properties?.name ||
                   node.properties?.title ||
                   node.properties?.policy_code ||
                   node.properties?.code ||
                   `节点${node.identity}`,
            type: node.labels?.[node.labels.length - 1] || 'Unknown',
            labels: node.labels || [],
            properties: node.properties || {}
          }
        })
        setSearchResults(nodes)
      }
    } catch (error) {
      console.error('搜索节点失败:', error)
      setSearchResults([])
    } finally {
      setLoading(false)
    }
  }, [searchTerm, selectedTypes])

  // 搜索防抖
  useEffect(() => {
    const timer = setTimeout(() => {
      searchNodes()
    }, 500)
    return () => clearTimeout(timer)
  }, [searchNodes])

  // 查看节点详情
  const viewNodeDetails = (node: NodeItem) => {
    setSelectedNode(node)
    setShowNodeDetails(true)
  }

  return (
    <div className="p-4 space-y-4 h-full overflow-auto">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">节点管理</h2>
          <p className="text-muted-foreground">
            浏览和管理图数据库中的所有节点数据
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={loadNodeTypes} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">类型概览</TabsTrigger>
          <TabsTrigger value="search">节点搜索</TabsTrigger>
        </TabsList>

        {/* 类型概览 */}
        <TabsContent value="overview" className="space-y-4">
          {/* 统计指标卡片 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Database className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">总节点数</p>
                    <p className="text-2xl font-bold">{totalNodes.toLocaleString()}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">节点类型</p>
                    <p className="text-2xl font-bold">{nodeTypes.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Activity className="h-5 w-5 text-orange-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">最大类型</p>
                    <p className="text-lg font-bold">
                      {nodeTypes.length > 0
                        ? nodeTypes.reduce((max, current) => current.count > max.count ? current : max).label
                        : '-'
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Download className="h-5 w-5 text-purple-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">平均数量</p>
                    <p className="text-2xl font-bold">
                      {nodeTypes.length > 0
                        ? Math.round(totalNodes / nodeTypes.length).toLocaleString()
                        : '0'
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 节点类型卡片 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {nodeTypes
              .sort((a, b) => b.count - a.count)
              .map((nodeType) => (
              <Card key={nodeType.label} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: nodeType.color }}
                      />
                      <div>
                        <h3 className="font-medium">{nodeType.label}</h3>
                        <p className="text-sm text-muted-foreground">
                          {nodeType.count.toLocaleString()} 个节点
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedTypes([nodeType.label])
                        setSearchTerm('')
                        setActiveTab('search')
                      }}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="mt-3">
                    <div className="w-full bg-muted rounded-full h-2">
                      <div
                        className="h-2 rounded-full transition-all duration-300"
                        style={{
                          backgroundColor: nodeType.color,
                          width: `${Math.min((nodeType.count / Math.max(...nodeTypes.map(n => n.count))) * 100, 100)}%`
                        }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {nodeTypes.length === 0 && !loading && (
            <Card>
              <CardContent className="py-8 text-center">
                <Database className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">暂无节点类型数据</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* 节点搜索 */}
        <TabsContent value="search" className="space-y-4">
          {/* 搜索控件 */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-4">
                {/* 节点类型多选下拉框 */}
                <div className="relative">
                  <div className="relative">
                    <button
                      data-dropdown-trigger
                      className="flex items-center justify-between w-48 px-3 py-2 text-sm border rounded-md bg-background hover:bg-muted/50 transition-colors"
                      onClick={() => {
                        const dropdown = document.getElementById('type-dropdown')
                        if (dropdown) {
                          dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none'
                        }
                      }}
                    >
                      <span className="truncate">{getSelectedTypesText()}</span>
                      <Filter className="h-4 w-4 ml-2 text-muted-foreground" />
                    </button>

                    <div
                      id="type-dropdown"
                      className="absolute top-full left-0 mt-1 w-full bg-background border rounded-md shadow-lg z-10 max-h-60 overflow-y-auto"
                      style={{ display: 'none' }}
                    >
                      <div className="p-2">
                        <label className="flex items-center space-x-2 p-2 hover:bg-muted rounded cursor-pointer">
                          <input
                            type="checkbox"
                            checked={selectedTypes.includes('all')}
                            onChange={() => handleTypeToggle('all')}
                            className="rounded"
                          />
                          <span className="text-sm">所有类型</span>
                        </label>

                        {nodeTypes.map((nodeType) => (
                          <label
                            key={nodeType.label}
                            className="flex items-center space-x-2 p-2 hover:bg-muted rounded cursor-pointer"
                          >
                            <input
                              type="checkbox"
                              checked={selectedTypes.includes(nodeType.label)}
                              onChange={() => handleTypeToggle(nodeType.label)}
                              className="rounded"
                            />
                            <div
                              className="w-3 h-3 rounded"
                              style={{
                                backgroundColor: nodeType.color || '#8993A4',
                                minWidth: '12px',
                                minHeight: '12px'
                              }}
                            />
                            <span className="text-sm">{nodeType.label}</span>
                            <span className="text-xs text-muted-foreground ml-auto">
                              {nodeType.count.toLocaleString()}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* 搜索框 */}
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="精准搜索节点 code 或 name（完全匹配）..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 搜索结果 */}
          {searchTerm && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  搜索结果 ({searchResults.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                    <span>搜索中...</span>
                  </div>
                ) : searchResults.length > 0 ? (
                  <div className="space-y-3">
                    {searchResults.map((node) => (
                      <div
                        key={node.id}
                        className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2">
                            {getNodeIcon(node.labels)}
                            <span className="font-medium">{node.label}</span>
                          </div>

                          <div className="flex items-center space-x-1">
                            {node.labels.map((label) => (
                              <Badge
                                key={label}
                                variant="secondary"
                                style={{
                                  backgroundColor: getNodeColor(label),
                                  color: 'white'
                                }}
                                className="text-xs"
                              >
                                {label}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => viewNodeDetails(node)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            查看
                          </Button>
                          <ChevronRight className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : searchTerm ? (
                  <div className="text-center py-8 text-muted-foreground">
                    没有找到匹配的节点
                  </div>
                ) : null}
              </CardContent>
            </Card>
          )}
        </TabsContent>


      </Tabs>

      {/* 节点详情弹窗 */}
      <Dialog open={showNodeDetails} onOpenChange={setShowNodeDetails}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                {selectedNode && getNodeIcon(selectedNode.labels)}
                <span>{selectedNode?.label}</span>
              </div>
              <div className="flex items-center space-x-1">
                {selectedNode?.labels.map((label) => (
                  <Badge
                    key={label}
                    variant="secondary"
                    style={{
                      backgroundColor: getNodeColor(label),
                      color: 'white'
                    }}
                    className="text-xs"
                  >
                    {label}
                  </Badge>
                ))}
              </div>
            </DialogTitle>
            <DialogDescription>
              节点ID: {selectedNode?.id}
            </DialogDescription>
          </DialogHeader>

          {selectedNode && (
            <div className="space-y-4">
              {/* 基本信息 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">基本信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-3 md:grid-cols-2">
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">节点ID:</span>
                      <p className="text-sm">{selectedNode.id}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">显示名称:</span>
                      <p className="text-sm">{selectedNode.label}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">主要类型:</span>
                      <p className="text-sm">{selectedNode.type}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">所有标签:</span>
                      <p className="text-sm">{selectedNode.labels.join(', ')}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 属性信息 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">属性信息</CardTitle>
                </CardHeader>
                <CardContent>
                  {Object.keys(selectedNode.properties).length > 0 ? (
                    <div className="space-y-3">
                      {Object.entries(selectedNode.properties).map(([key, value]) => (
                        <div key={key} className="grid grid-cols-3 gap-3">
                          <span className="text-sm font-medium text-muted-foreground">
                            {key}:
                          </span>
                          <span className="text-sm col-span-2 break-all">
                            {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                          </span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">暂无属性信息</p>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}