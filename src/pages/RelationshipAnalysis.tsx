import { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  Search,
  RefreshCw,
  Eye,
  Network,
  GitBranch,
  Users,
  Building2,
  FileText,
  MapPin,
  Activity,
  TrendingUp,
  BarChart3,
  ArrowRight,
  Filter,
  Target,
  Zap,
  Layers,
  PieChart
} from 'lucide-react'
import { graphApi } from '@/services/api'
import { ensureInitialized } from '@/services/globalInitService'
import { getNodeColor } from '@/lib/colorManager'

// 关系类型定义
interface RelationshipItem {
  id: string
  type: string
  sourceNode: {
    id: string
    label: string
    labels: string[]
    properties: Record<string, any>
  }
  targetNode: {
    id: string
    label: string
    labels: string[]
    properties: Record<string, any>
  }
  properties: Record<string, any>
}

interface RelationshipTypeStats {
  type: string
  count: number
  description: string
}

interface PathResult {
  path: any[]
  length: number
  nodes: any[]
  relationships: any[]
}

// 模式分析相关类型
interface NodeDegreeResult {
  node: {
    id: string
    label: string
    labels: string[]
    properties: Record<string, any>
  }
  degree: number
  inDegree?: number
  outDegree?: number
}

interface CentralityResult {
  node: {
    id: string
    label: string
    labels: string[]
    properties: Record<string, any>
  }
  betweennessCentrality?: number
  closenessCentrality?: number
  pageRank?: number
}

interface CommunityResult {
  communityId: number
  nodes: Array<{
    id: string
    label: string
    labels: string[]
    properties: Record<string, any>
  }>
  size: number
}

interface RelationshipStrengthResult {
  type: string
  count: number
  avgProperties: number
  strength: number
  description: string
}

export function RelationshipAnalysis() {
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  // 关系统计数据
  const [relationshipTypes, setRelationshipTypes] = useState<RelationshipTypeStats[]>([])
  const [totalRelationships, setTotalRelationships] = useState(0)

  // 关系搜索
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRelType, setSelectedRelType] = useState('all')
  const [searchResults, setSearchResults] = useState<RelationshipItem[]>([])

  // 路径分析
  const [startNodeId, setStartNodeId] = useState('')
  const [endNodeId, setEndNodeId] = useState('')
  const [pathResults, setPathResults] = useState<PathResult[]>([])

  // 详情弹窗
  const [selectedRelationship, setSelectedRelationship] = useState<RelationshipItem | null>(null)
  const [showRelDetails, setShowRelDetails] = useState(false)

  // 模式分析状态
  const [nodeDegreeResults, setNodeDegreeResults] = useState<NodeDegreeResult[]>([])
  const [centralityResults, setCentralityResults] = useState<CentralityResult[]>([])
  const [communityResults, setCommunityResults] = useState<CommunityResult[]>([])
  const [relationshipStrengthResults, setRelationshipStrengthResults] = useState<RelationshipStrengthResult[]>([])
  const [patternAnalysisLoading, setPatternAnalysisLoading] = useState(false)

  // 节点详情弹窗
  const [selectedNode, setSelectedNode] = useState<any>(null)
  const [showNodeDetails, setShowNodeDetails] = useState(false)

  // 分析进度状态
  const [analysisProgress, setAnalysisProgress] = useState({
    nodeDegree: false,
    centrality: false,
    community: false,
    relationshipStrength: false
  })

  // 关系类型描述映射
  const relationshipDescriptions: Record<string, string> = {
    'PARENT_OF': '父子层级关系',
    'LOCATED_IN': '地理位置关系',
    'MANAGED_BY': '管理关系',
    'SUPERVISED_BY': '监督关系',
    'BELONGS_TO_TEAM': '团队归属关系',
    'BELONGS_TO_CHANNEL': '渠道归属关系',
    'BELONGS_TO_POLICY': '保单归属关系',
    'BELONGS_TO_INDUSTRY': '行业归属关系',
    'INSURED_BY': '投保关系',
    'COVERS': '承保关系',
    'COVERS_PRODUCT': '产品承保关系',
    'COVERS_INSURED': '被保险人承保关系',
    'UNDERWRITTEN_BY': '承保公司关系',
    'DISTRIBUTED_BY': '销售渠道关系',
    'SOLD_BY': '销售关系',
    'ISSUED_BY': '签发关系',
    'OCCURRED_IN': '发生地关系',
    'INVOLVES_INSURED_PERSON': '涉及被保险人关系',
    'HAS_PHONE': '电话联系方式',
    'HAS_EMAIL': '邮箱联系方式',
    'HAS_ADDITIONAL_PHONE': '附加电话',
    'HAS_ADDITIONAL_EMAIL': '附加邮箱',
    'BLACKLISTED': '黑名单关系',
    'LINKS_TO_REGION': '区域链接关系'
  }

  // 获取关系类型图标
  const getRelationshipIcon = (type: string) => {
    if (type.includes('PARENT') || type.includes('HIERARCHY')) return <GitBranch className="h-4 w-4" />
    if (type.includes('LOCATED') || type.includes('REGION')) return <MapPin className="h-4 w-4" />
    if (type.includes('MANAGED') || type.includes('SUPERVISED')) return <Users className="h-4 w-4" />
    if (type.includes('POLICY') || type.includes('INSURED')) return <FileText className="h-4 w-4" />
    if (type.includes('ORGANIZATION') || type.includes('COMPANY')) return <Building2 className="h-4 w-4" />
    return <Network className="h-4 w-4" />
  }

  // 初始化数据
  useEffect(() => {
    loadRelationshipTypes()
  }, [])

  // 加载关系类型统计
  const loadRelationshipTypes = async () => {
    try {
      setLoading(true)
      await ensureInitialized()

      const statsData = await graphApi.getOverview()

      if (statsData.relationships_by_type) {
        const relTypeStats: RelationshipTypeStats[] = Object.entries(statsData.relationships_by_type)
          .map(([type, count]) => ({
            type,
            count: count as number,
            description: relationshipDescriptions[type] || '未知关系类型'
          }))
          .sort((a, b) => b.count - a.count)

        setRelationshipTypes(relTypeStats)
        setTotalRelationships(statsData.total_relationships || 0)
      }
    } catch (error) {
      console.error('加载关系类型失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 搜索关系
  const searchRelationships = useCallback(async () => {
    if (!searchTerm.trim()) {
      setSearchResults([])
      return
    }

    try {
      setLoading(true)

      // 构建关系搜索查询
      let cypherQuery = ''
      if (selectedRelType === 'all') {
        cypherQuery = `
          MATCH (a)-[r]->(b)
          WHERE a.name CONTAINS '${searchTerm}' OR a.code CONTAINS '${searchTerm}'
             OR b.name CONTAINS '${searchTerm}' OR b.code CONTAINS '${searchTerm}'
          RETURN a, r, b, labels(a) as a_labels, labels(b) as b_labels, type(r) as rel_type
          LIMIT 50
        `
      } else {
        cypherQuery = `
          MATCH (a)-[r:${selectedRelType}]->(b)
          WHERE a.name CONTAINS '${searchTerm}' OR a.code CONTAINS '${searchTerm}'
             OR b.name CONTAINS '${searchTerm}' OR b.code CONTAINS '${searchTerm}'
          RETURN a, r, b, labels(a) as a_labels, labels(b) as b_labels, type(r) as rel_type
          LIMIT 50
        `
      }

      const result = await graphApi.executeCypher({ query: cypherQuery })

      if (result.result?.nodes?.a && result.result?.nodes?.b && result.result?.relationships) {
        const relationships: RelationshipItem[] = result.result.nodes.a.map((sourceNode: any, index: number) => {
          const targetNode = result.result.nodes.b[index]
          const relationship = result.result.relationships[index] || {}

          return {
            id: relationship.identity || `${sourceNode.identity}-${targetNode.identity}`,
            type: relationship.type || 'UNKNOWN',
            sourceNode: {
              id: sourceNode.properties?.business_id || sourceNode.identity,
              label: sourceNode.properties?.name || sourceNode.properties?.code || `节点${sourceNode.identity}`,
              labels: sourceNode.labels || [],
              properties: sourceNode.properties || {}
            },
            targetNode: {
              id: targetNode.properties?.business_id || targetNode.identity,
              label: targetNode.properties?.name || targetNode.properties?.code || `节点${targetNode.identity}`,
              labels: targetNode.labels || [],
              properties: targetNode.properties || {}
            },
            properties: relationship.properties || {}
          }
        })
        setSearchResults(relationships)
      }
    } catch (error) {
      console.error('搜索关系失败:', error)
      setSearchResults([])
    } finally {
      setLoading(false)
    }
  }, [searchTerm, selectedRelType])

  // 搜索防抖
  useEffect(() => {
    const timer = setTimeout(() => {
      searchRelationships()
    }, 500)
    return () => clearTimeout(timer)
  }, [searchRelationships])

  // 路径分析
  const analyzeShortestPath = async () => {
    if (!startNodeId.trim() || !endNodeId.trim()) {
      return
    }

    try {
      setLoading(true)
      const result = await graphApi.getShortestPath({
        start_node_id: startNodeId,
        end_node_id: endNodeId,
        max_depth: 5
      })

      if (result.result?.nodes) {
        // 处理路径结果
        const paths: PathResult[] = [{
          path: result.result.nodes.p || [],
          length: result.result.nodes.path_nodes?.length || 0,
          nodes: result.result.nodes.path_nodes || [],
          relationships: []
        }]
        setPathResults(paths)
      }
    } catch (error) {
      console.error('路径分析失败:', error)
      setPathResults([])
    } finally {
      setLoading(false)
    }
  }

  // 查看关系详情
  const viewRelationshipDetails = (relationship: RelationshipItem) => {
    setSelectedRelationship(relationship)
    setShowRelDetails(true)
  }

  // 节点度分析
  const analyzeNodeDegree = async () => {
    try {
      setPatternAnalysisLoading(true)
      await ensureInitialized()

      // 使用直接的Cypher查询来确保数据格式正确
      const cypherQuery = `
        MATCH (n)
        OPTIONAL MATCH (n)-[r]-()
        RETURN n, labels(n) as n_labels, count(r) as degree
        ORDER BY degree DESC
        LIMIT 20
      `

      const result = await graphApi.executeCypher({ query: cypherQuery })

      console.log('节点度分析原始结果:', result) // 调试日志

      if (result.result?.nodes?.n) {
        const degreeResults: NodeDegreeResult[] = result.result.nodes.n.map((node: any, index: number) => {
          // 尝试从不同可能的路径获取度数
          let degree = 0

          // 检查各种可能的数据路径
          if (result.result.degree && result.result.degree[index] !== undefined) {
            degree = result.result.degree[index]
          } else if (result.result.data && result.result.data[index]) {
            // 如果data是对象数组
            if (typeof result.result.data[index] === 'object' && result.result.data[index].degree !== undefined) {
              degree = result.result.data[index].degree
            }
            // 如果data是值数组，且我们知道degree是第三个值（n, n_labels, degree）
            else if (Array.isArray(result.result.data[index]) && result.result.data[index].length >= 3) {
              degree = result.result.data[index][2] // degree是第三个返回值
            }
          }

          console.log(`节点 ${index} 度数:`, degree, '原始数据:', result.result.data?.[index], '度数数组:', result.result.degree?.[index]) // 调试日志

          return {
            node: {
              id: node.properties?.business_id || node.identity,
              label: node.properties?.name || node.properties?.code || `节点${node.identity}`,
              labels: node.labels || [],
              properties: node.properties || {}
            },
            degree: degree
          }
        })
        setNodeDegreeResults(degreeResults)
      }
    } catch (error) {
      console.error('节点度分析失败:', error)
      setNodeDegreeResults([])
    } finally {
      setPatternAnalysisLoading(false)
    }
  }

  // 中心性分析
  const analyzeCentrality = async () => {
    try {
      setPatternAnalysisLoading(true)
      await ensureInitialized()

      // 使用PageRank算法进行中心性分析
      const cypherQuery = `
        CALL gds.pageRank.stream('myGraph')
        YIELD nodeId, score
        RETURN gds.util.asNode(nodeId) as node, score as pageRank
        ORDER BY pageRank DESC
        LIMIT 20
      `

      // 如果GDS不可用，使用简单的度中心性
      const fallbackQuery = `
        MATCH (n)
        OPTIONAL MATCH (n)-[r]-()
        WITH n, count(r) as degree
        RETURN n, labels(n) as n_labels, degree
        ORDER BY degree DESC
        LIMIT 20
      `

      try {
        const result = await graphApi.executeCypher({ query: cypherQuery })
        // 处理GDS结果
        if (result.result?.nodes?.node) {
          const centralityResults: CentralityResult[] = result.result.nodes.node.map((node: any, index: number) => {
            const pageRank = result.result.data?.[index]?.pageRank || 0
            return {
              node: {
                id: node.properties?.business_id || node.identity,
                label: node.properties?.name || node.properties?.code || `节点${node.identity}`,
                labels: node.labels || [],
                properties: node.properties || {}
              },
              pageRank: pageRank
            }
          })
          setCentralityResults(centralityResults)
        }
      } catch (gdsError) {
        // 如果GDS不可用，使用备用查询
        const result = await graphApi.executeCypher({ query: fallbackQuery })
        if (result.result?.nodes?.n) {
          const centralityResults: CentralityResult[] = result.result.nodes.n.map((node: any, index: number) => {
            const degree = result.result.data?.[index]?.degree || 0
            return {
              node: {
                id: node.properties?.business_id || node.identity,
                label: node.properties?.name || node.properties?.code || `节点${node.identity}`,
                labels: node.labels || [],
                properties: node.properties || {}
              },
              betweennessCentrality: degree // 使用度作为中心性的近似
            }
          })
          setCentralityResults(centralityResults)
        }
      }
    } catch (error) {
      console.error('中心性分析失败:', error)
      setCentralityResults([])
    } finally {
      setPatternAnalysisLoading(false)
    }
  }

  // 社区检测
  const detectCommunities = async () => {
    try {
      setPatternAnalysisLoading(true)
      await ensureInitialized()

      // 使用标签传播算法进行社区检测
      const cypherQuery = `
        CALL gds.labelPropagation.stream('myGraph')
        YIELD nodeId, communityId
        RETURN gds.util.asNode(nodeId) as node, communityId
        ORDER BY communityId
      `

      // 备用查询：基于节点标签进行简单聚类
      const fallbackQuery = `
        MATCH (n)
        WITH labels(n)[0] as primaryLabel, collect(n) as nodes
        WHERE primaryLabel IS NOT NULL
        RETURN primaryLabel as communityId, nodes
        ORDER BY size(nodes) DESC
        LIMIT 10
      `

      try {
        const result = await graphApi.executeCypher({ query: cypherQuery })
        // 处理GDS结果
        if (result.result?.nodes?.node) {
          const communityMap = new Map<number, any[]>()

          result.result.nodes.node.forEach((node: any, index: number) => {
            const communityId = result.result.data?.[index]?.communityId || 0
            if (!communityMap.has(communityId)) {
              communityMap.set(communityId, [])
            }
            communityMap.get(communityId)?.push({
              id: node.properties?.business_id || node.identity,
              label: node.properties?.name || node.properties?.code || `节点${node.identity}`,
              labels: node.labels || [],
              properties: node.properties || {}
            })
          })

          const communities: CommunityResult[] = Array.from(communityMap.entries()).map(([communityId, nodes]) => ({
            communityId,
            nodes,
            size: nodes.length
          })).sort((a, b) => b.size - a.size).slice(0, 10)

          setCommunityResults(communities)
        }
      } catch (gdsError) {
        // 使用备用查询
        const result = await graphApi.executeCypher({ query: fallbackQuery })
        if (result.result?.data) {
          const communities: CommunityResult[] = result.result.data.map((item: any, index: number) => {
            const nodes = item.nodes?.map((node: any) => ({
              id: node.properties?.business_id || node.identity,
              label: node.properties?.name || node.properties?.code || `节点${node.identity}`,
              labels: node.labels || [],
              properties: node.properties || {}
            })) || []

            return {
              communityId: index,
              nodes,
              size: nodes.length
            }
          }).filter(community => community.size > 0)

          setCommunityResults(communities)
        }
      }
    } catch (error) {
      console.error('社区检测失败:', error)
      setCommunityResults([])
    } finally {
      setPatternAnalysisLoading(false)
    }
  }

  // 关系强度分析
  const analyzeRelationshipStrength = async () => {
    try {
      setPatternAnalysisLoading(true)
      await ensureInitialized()

      const cypherQuery = `
        MATCH ()-[r]->()
        WITH type(r) as relType, count(r) as relCount,
             avg(size(keys(r))) as avgProperties
        RETURN relType, relCount, avgProperties,
               relCount * (1 + avgProperties * 0.1) as strength
        ORDER BY strength DESC
        LIMIT 15
      `

      const result = await graphApi.executeCypher({ query: cypherQuery })

      if (result.result?.data) {
        const strengthResults: RelationshipStrengthResult[] = result.result.data.map((item: any) => ({
          type: item.relType || 'UNKNOWN',
          count: item.relCount || 0,
          avgProperties: Math.round((item.avgProperties || 0) * 100) / 100,
          strength: Math.round((item.strength || 0) * 100) / 100,
          description: relationshipDescriptions[item.relType] || '未知关系类型'
        }))
        setRelationshipStrengthResults(strengthResults)
      }
    } catch (error) {
      console.error('关系强度分析失败:', error)
      setRelationshipStrengthResults([])
    } finally {
      setPatternAnalysisLoading(false)
    }
  }

  // 批量执行所有模式分析
  const runAllPatternAnalysis = async () => {
    try {
      setPatternAnalysisLoading(true)
      setAnalysisProgress({
        nodeDegree: false,
        centrality: false,
        community: false,
        relationshipStrength: false
      })

      // 顺序执行分析以显示进度
      setAnalysisProgress(prev => ({ ...prev, nodeDegree: true }))
      await analyzeNodeDegree()

      setAnalysisProgress(prev => ({ ...prev, centrality: true }))
      await analyzeCentrality()

      setAnalysisProgress(prev => ({ ...prev, community: true }))
      await detectCommunities()

      setAnalysisProgress(prev => ({ ...prev, relationshipStrength: true }))
      await analyzeRelationshipStrength()

    } catch (error) {
      console.error('批量分析失败:', error)
    } finally {
      setPatternAnalysisLoading(false)
      setAnalysisProgress({
        nodeDegree: false,
        centrality: false,
        community: false,
        relationshipStrength: false
      })
    }
  }

  // 清空所有分析结果
  const clearAllResults = () => {
    setNodeDegreeResults([])
    setCentralityResults([])
    setCommunityResults([])
    setRelationshipStrengthResults([])
  }

  // 查看节点详情
  const viewNodeDetails = (node: any) => {
    setSelectedNode(node)
    setShowNodeDetails(true)
  }

  // 临时测试函数 - 用于调试数据结构
  const testDataStructure = async () => {
    try {
      const testQuery = `
        MATCH (n)
        OPTIONAL MATCH (n)-[r]-()
        RETURN n, count(r) as degree
        ORDER BY degree DESC
        LIMIT 5
      `
      const result = await graphApi.executeCypher({ query: testQuery })
      console.log('测试查询完整结果:', JSON.stringify(result, null, 2))
    } catch (error) {
      console.error('测试查询失败:', error)
    }
  }

  // 导出分析结果
  const exportAnalysisResults = () => {
    const results = {
      timestamp: new Date().toISOString(),
      nodeDegreeAnalysis: nodeDegreeResults.map(r => ({
        nodeId: r.node.id,
        nodeLabel: r.node.label,
        nodeLabels: r.node.labels,
        degree: r.degree
      })),
      centralityAnalysis: centralityResults.map(r => ({
        nodeId: r.node.id,
        nodeLabel: r.node.label,
        nodeLabels: r.node.labels,
        pageRank: r.pageRank,
        betweennessCentrality: r.betweennessCentrality,
        closenessCentrality: r.closenessCentrality
      })),
      communityDetection: communityResults.map(c => ({
        communityId: c.communityId,
        size: c.size,
        nodes: c.nodes.map(n => ({
          nodeId: n.id,
          nodeLabel: n.label,
          nodeLabels: n.labels
        }))
      })),
      relationshipStrength: relationshipStrengthResults.map(r => ({
        type: r.type,
        count: r.count,
        avgProperties: r.avgProperties,
        strength: r.strength,
        description: r.description
      }))
    }

    const dataStr = JSON.stringify(results, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `pattern-analysis-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="p-4 space-y-4 h-full overflow-auto">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">关系分析</h2>
          <p className="text-muted-foreground">
            分析和探索图数据库中的节点关系模式
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={loadRelationshipTypes} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">关系概览</TabsTrigger>
          <TabsTrigger value="search">关系搜索</TabsTrigger>
          <TabsTrigger value="path">路径分析</TabsTrigger>
          <TabsTrigger value="patterns">模式分析</TabsTrigger>
        </TabsList>

        {/* 关系概览 */}
        <TabsContent value="overview" className="space-y-4">
          {/* 统计指标卡片 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Network className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">总关系数</p>
                    <p className="text-2xl font-bold">{totalRelationships.toLocaleString()}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">关系类型</p>
                    <p className="text-2xl font-bold">{relationshipTypes.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Activity className="h-5 w-5 text-orange-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">最多类型</p>
                    <p className="text-lg font-bold">
                      {relationshipTypes.length > 0
                        ? relationshipTypes[0].type.replace(/_/g, ' ')
                        : '-'
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5 text-purple-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">平均数量</p>
                    <p className="text-2xl font-bold">
                      {relationshipTypes.length > 0
                        ? Math.round(totalRelationships / relationshipTypes.length).toLocaleString()
                        : '0'
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 关系类型列表 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {relationshipTypes.map((relType) => (
              <Card key={relType.type} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getRelationshipIcon(relType.type)}
                      <div>
                        <h3 className="font-medium">{relType.type.replace(/_/g, ' ')}</h3>
                        <p className="text-sm text-muted-foreground">
                          {relType.count.toLocaleString()} 个关系
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {relType.description}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedRelType(relType.type)
                        setSearchTerm('')
                        setActiveTab('search')
                      }}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="mt-3">
                    <div className="w-full bg-muted rounded-full h-2">
                      <div
                        className="h-2 rounded-full transition-all duration-300 bg-blue-500"
                        style={{
                          width: `${Math.min((relType.count / Math.max(...relationshipTypes.map(r => r.count))) * 100, 100)}%`
                        }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {relationshipTypes.length === 0 && !loading && (
            <Card>
              <CardContent className="py-8 text-center">
                <Network className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">暂无关系类型数据</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* 关系搜索 */}
        <TabsContent value="search" className="space-y-4">
          {/* 搜索控件 */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-4">
                {/* 关系类型选择 */}
                <div className="flex items-center space-x-2">
                  <Filter className="h-4 w-4 text-muted-foreground" />
                  <select
                    value={selectedRelType}
                    onChange={(e) => setSelectedRelType(e.target.value)}
                    className="px-3 py-2 border rounded-md text-sm w-48"
                  >
                    <option value="all">所有关系类型</option>
                    {relationshipTypes.map((relType) => (
                      <option key={relType.type} value={relType.type}>
                        {relType.type.replace(/_/g, ' ')} ({relType.count})
                      </option>
                    ))}
                  </select>
                </div>

                {/* 搜索框 */}
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索关系中的节点名称或代码..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 搜索结果 */}
          {searchTerm && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  搜索结果 ({searchResults.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                    <span>搜索中...</span>
                  </div>
                ) : searchResults.length > 0 ? (
                  <div className="space-y-3">
                    {searchResults.map((relationship) => (
                      <div
                        key={relationship.id}
                        className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-center space-x-4 flex-1">
                          {/* 源节点 */}
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center space-x-1">
                              {relationship.sourceNode.labels.map((label) => (
                                <div
                                  key={label}
                                  className="w-3 h-3 rounded"
                                  style={{ backgroundColor: getNodeColor(label) }}
                                />
                              ))}
                            </div>
                            <span className="font-medium text-sm">
                              {relationship.sourceNode.label}
                            </span>
                          </div>

                          {/* 关系 */}
                          <div className="flex items-center space-x-2">
                            <ArrowRight className="h-4 w-4 text-muted-foreground" />
                            <Badge variant="outline" className="text-xs">
                              {relationship.type.replace(/_/g, ' ')}
                            </Badge>
                            <ArrowRight className="h-4 w-4 text-muted-foreground" />
                          </div>

                          {/* 目标节点 */}
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center space-x-1">
                              {relationship.targetNode.labels.map((label) => (
                                <div
                                  key={label}
                                  className="w-3 h-3 rounded"
                                  style={{ backgroundColor: getNodeColor(label) }}
                                />
                              ))}
                            </div>
                            <span className="font-medium text-sm">
                              {relationship.targetNode.label}
                            </span>
                          </div>
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => viewRelationshipDetails(relationship)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          查看
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : searchTerm ? (
                  <div className="text-center py-8 text-muted-foreground">
                    没有找到匹配的关系
                  </div>
                ) : null}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* 路径分析 */}
        <TabsContent value="path" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>最短路径分析</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="text-sm font-medium">起始节点ID</label>
                  <Input
                    placeholder="输入起始节点的ID或代码"
                    value={startNodeId}
                    onChange={(e) => setStartNodeId(e.target.value)}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">目标节点ID</label>
                  <Input
                    placeholder="输入目标节点的ID或代码"
                    value={endNodeId}
                    onChange={(e) => setEndNodeId(e.target.value)}
                  />
                </div>
              </div>

              <Button
                onClick={analyzeShortestPath}
                disabled={!startNodeId.trim() || !endNodeId.trim() || loading}
                className="w-full"
              >
                {loading ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <GitBranch className="h-4 w-4 mr-2" />
                )}
                分析最短路径
              </Button>

              {/* 路径结果 */}
              {pathResults.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-medium">路径结果</h4>
                  {pathResults.map((path, index) => (
                    <Card key={index}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium">路径 {index + 1}</span>
                          <Badge variant="secondary">长度: {path.length}</Badge>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {path.nodes.length > 0 ? (
                            <div className="flex items-center space-x-2 flex-wrap">
                              {path.nodes.map((node: any, nodeIndex: number) => (
                                <div key={nodeIndex} className="flex items-center space-x-1">
                                  <span className="px-2 py-1 bg-muted rounded text-xs">
                                    {node.properties?.name || node.properties?.code || `节点${node.identity}`}
                                  </span>
                                  {nodeIndex < path.nodes.length - 1 && (
                                    <ArrowRight className="h-3 w-3" />
                                  )}
                                </div>
                              ))}
                            </div>
                          ) : (
                            <span>未找到路径</span>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 模式分析 */}
        <TabsContent value="patterns" className="space-y-4">
          {/* 批量操作控制栏 */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">模式分析工具</h3>
                  <p className="text-sm text-muted-foreground">
                    分析图数据中的结构模式和关键节点
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={testDataStructure}
                    className="bg-yellow-50 border-yellow-200 text-yellow-800"
                  >
                    🔍 测试数据
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={exportAnalysisResults}
                    disabled={patternAnalysisLoading || (
                      nodeDegreeResults.length === 0 &&
                      centralityResults.length === 0 &&
                      communityResults.length === 0 &&
                      relationshipStrengthResults.length === 0
                    )}
                  >
                    <Layers className="h-4 w-4 mr-2" />
                    导出结果
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearAllResults}
                    disabled={patternAnalysisLoading}
                  >
                    <Target className="h-4 w-4 mr-2" />
                    清空结果
                  </Button>
                  <Button
                    onClick={runAllPatternAnalysis}
                    disabled={patternAnalysisLoading}
                    size="sm"
                  >
                    {patternAnalysisLoading ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Zap className="h-4 w-4 mr-2" />
                    )}
                    运行全部分析
                  </Button>
                </div>
              </div>

              {/* 分析进度指示器 */}
              {patternAnalysisLoading && (
                <div className="mt-4 space-y-2">
                  <div className="text-sm text-muted-foreground">分析进度:</div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <div className={`flex items-center space-x-2 text-xs ${analysisProgress.nodeDegree ? 'text-blue-600' : 'text-muted-foreground'}`}>
                      {analysisProgress.nodeDegree ? (
                        <RefreshCw className="h-3 w-3 animate-spin" />
                      ) : (
                        <div className="h-3 w-3 rounded-full border-2 border-muted" />
                      )}
                      <span>节点度分析</span>
                    </div>
                    <div className={`flex items-center space-x-2 text-xs ${analysisProgress.centrality ? 'text-blue-600' : 'text-muted-foreground'}`}>
                      {analysisProgress.centrality ? (
                        <RefreshCw className="h-3 w-3 animate-spin" />
                      ) : (
                        <div className="h-3 w-3 rounded-full border-2 border-muted" />
                      )}
                      <span>中心性分析</span>
                    </div>
                    <div className={`flex items-center space-x-2 text-xs ${analysisProgress.community ? 'text-blue-600' : 'text-muted-foreground'}`}>
                      {analysisProgress.community ? (
                        <RefreshCw className="h-3 w-3 animate-spin" />
                      ) : (
                        <div className="h-3 w-3 rounded-full border-2 border-muted" />
                      )}
                      <span>社区检测</span>
                    </div>
                    <div className={`flex items-center space-x-2 text-xs ${analysisProgress.relationshipStrength ? 'text-blue-600' : 'text-muted-foreground'}`}>
                      {analysisProgress.relationshipStrength ? (
                        <RefreshCw className="h-3 w-3 animate-spin" />
                      ) : (
                        <div className="h-3 w-3 rounded-full border-2 border-muted" />
                      )}
                      <span>关系强度分析</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            {/* 节点度分析 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center justify-between">
                  节点度分析
                  <Badge variant="secondary" className="text-xs">
                    {nodeDegreeResults.length} 个结果
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-3">
                  分析节点的连接度，找出图中的关键节点
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={analyzeNodeDegree}
                  disabled={patternAnalysisLoading}
                  className="mb-4"
                >
                  {patternAnalysisLoading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Activity className="h-4 w-4 mr-2" />
                  )}
                  开始分析
                </Button>

                {/* 节点度分析结果 */}
                {nodeDegreeResults.length > 0 && (
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {nodeDegreeResults.slice(0, 10).map((result, index) => (
                      <div
                        key={result.node.id}
                        className="flex items-center justify-between p-2 border rounded text-sm hover:bg-muted/50 cursor-pointer transition-colors"
                        onClick={() => viewNodeDetails(result.node)}
                      >
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-muted-foreground w-6">#{index + 1}</span>
                          <div className="flex items-center space-x-1">
                            {result.node.labels.map((label) => (
                              <div
                                key={label}
                                className="w-2 h-2 rounded"
                                style={{ backgroundColor: getNodeColor(label) }}
                              />
                            ))}
                          </div>
                          <span className="font-medium truncate max-w-32">
                            {result.node.label}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">
                            度: {result.degree}
                          </Badge>
                          <Eye className="h-3 w-3 text-muted-foreground" />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 社区检测 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center justify-between">
                  社区检测
                  <Badge variant="secondary" className="text-xs">
                    {communityResults.length} 个社区
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-3">
                  识别图中的社区结构和节点聚类
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={detectCommunities}
                  disabled={patternAnalysisLoading}
                  className="mb-4"
                >
                  {patternAnalysisLoading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Users className="h-4 w-4 mr-2" />
                  )}
                  检测社区
                </Button>

                {/* 社区检测结果 */}
                {communityResults.length > 0 && (
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {communityResults.slice(0, 5).map((community, index) => (
                      <div key={community.communityId} className="p-2 border rounded">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium">
                            社区 {community.communityId}
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {community.size} 个节点
                          </Badge>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {community.nodes.slice(0, 6).map((node) => (
                            <div key={node.id} className="flex items-center space-x-1">
                              <div className="flex items-center space-x-1">
                                {node.labels.map((label) => (
                                  <div
                                    key={label}
                                    className="w-1.5 h-1.5 rounded"
                                    style={{ backgroundColor: getNodeColor(label) }}
                                  />
                                ))}
                              </div>
                              <span className="text-xs text-muted-foreground truncate max-w-16">
                                {node.label}
                              </span>
                            </div>
                          ))}
                          {community.nodes.length > 6 && (
                            <span className="text-xs text-muted-foreground">
                              +{community.nodes.length - 6} 更多
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 中心性分析 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center justify-between">
                  中心性分析
                  <Badge variant="secondary" className="text-xs">
                    {centralityResults.length} 个结果
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-3">
                  计算节点的中心性指标，识别重要节点
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={analyzeCentrality}
                  disabled={patternAnalysisLoading}
                  className="mb-4"
                >
                  {patternAnalysisLoading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <TrendingUp className="h-4 w-4 mr-2" />
                  )}
                  分析中心性
                </Button>

                {/* 中心性分析结果 */}
                {centralityResults.length > 0 && (
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {centralityResults.slice(0, 10).map((result, index) => (
                      <div
                        key={result.node.id}
                        className="flex items-center justify-between p-2 border rounded text-sm hover:bg-muted/50 cursor-pointer transition-colors"
                        onClick={() => viewNodeDetails(result.node)}
                      >
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-muted-foreground w-6">#{index + 1}</span>
                          <div className="flex items-center space-x-1">
                            {result.node.labels.map((label) => (
                              <div
                                key={label}
                                className="w-2 h-2 rounded"
                                style={{ backgroundColor: getNodeColor(label) }}
                              />
                            ))}
                          </div>
                          <span className="font-medium truncate max-w-32">
                            {result.node.label}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">
                            {result.pageRank ? `PR: ${result.pageRank.toFixed(4)}` :
                             result.betweennessCentrality ? `BC: ${result.betweennessCentrality}` :
                             result.closenessCentrality ? `CC: ${result.closenessCentrality.toFixed(4)}` : 'N/A'}
                          </Badge>
                          <Eye className="h-3 w-3 text-muted-foreground" />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 关系强度分析 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center justify-between">
                  关系强度分析
                  <Badge variant="secondary" className="text-xs">
                    {relationshipStrengthResults.length} 个类型
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-3">
                  分析不同关系类型的强度和重要性
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={analyzeRelationshipStrength}
                  disabled={patternAnalysisLoading}
                  className="mb-4"
                >
                  {patternAnalysisLoading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <BarChart3 className="h-4 w-4 mr-2" />
                  )}
                  分析强度
                </Button>

                {/* 关系强度分析结果 */}
                {relationshipStrengthResults.length > 0 && (
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {relationshipStrengthResults.slice(0, 8).map((result, index) => (
                      <div key={result.type} className="p-2 border rounded">
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center space-x-2">
                            {getRelationshipIcon(result.type)}
                            <span className="text-sm font-medium">
                              {result.type.replace(/_/g, ' ')}
                            </span>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            强度: {result.strength}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {result.count} 个关系 • 平均属性: {result.avgProperties}
                        </div>
                        <div className="mt-1">
                          <div className="w-full bg-muted rounded-full h-1.5">
                            <div
                              className="h-1.5 rounded-full transition-all duration-300 bg-gradient-to-r from-blue-500 to-purple-500"
                              style={{
                                width: `${Math.min((result.strength / Math.max(...relationshipStrengthResults.map(r => r.strength))) * 100, 100)}%`
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 模式分析总览 */}
          {(nodeDegreeResults.length > 0 || centralityResults.length > 0 ||
            communityResults.length > 0 || relationshipStrengthResults.length > 0) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2">
                  <PieChart className="h-5 w-5" />
                  <span>模式分析总览</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-500">
                      {nodeDegreeResults.length > 0 ? nodeDegreeResults[0]?.degree || 0 : 0}
                    </div>
                    <div className="text-sm text-muted-foreground">最高节点度</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-500">
                      {communityResults.length}
                    </div>
                    <div className="text-sm text-muted-foreground">检测到社区</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-500">
                      {centralityResults.length > 0 ?
                        (centralityResults[0]?.pageRank?.toFixed(4) ||
                         centralityResults[0]?.betweennessCentrality || 'N/A') : 0}
                    </div>
                    <div className="text-sm text-muted-foreground">最高中心性</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-500">
                      {relationshipStrengthResults.length > 0 ?
                        relationshipStrengthResults[0]?.strength?.toFixed(1) || 0 : 0}
                    </div>
                    <div className="text-sm text-muted-foreground">最强关系强度</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* 关系详情弹窗 */}
      <Dialog open={showRelDetails} onOpenChange={setShowRelDetails}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                {getRelationshipIcon(selectedRelationship?.type || '')}
                <span>{selectedRelationship?.type.replace(/_/g, ' ')}</span>
              </div>
              <Badge variant="outline">
                {relationshipDescriptions[selectedRelationship?.type || ''] || '未知关系类型'}
              </Badge>
            </DialogTitle>
          </DialogHeader>

          {selectedRelationship && (
            <div className="space-y-4">
              {/* 关系基本信息 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">关系信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-3 md:grid-cols-2">
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">关系ID:</span>
                      <p className="text-sm">{selectedRelationship.id}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">关系类型:</span>
                      <p className="text-sm">{selectedRelationship.type}</p>
                    </div>
                    <div className="md:col-span-2">
                      <span className="text-sm font-medium text-muted-foreground">描述:</span>
                      <p className="text-sm">{relationshipDescriptions[selectedRelationship.type] || '未知关系类型'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 源节点信息 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center space-x-2">
                    <span>源节点</span>
                    <div className="flex items-center space-x-1">
                      {selectedRelationship.sourceNode.labels.map((label) => (
                        <Badge
                          key={label}
                          variant="secondary"
                          style={{
                            backgroundColor: getNodeColor(label),
                            color: 'white'
                          }}
                          className="text-xs"
                        >
                          {label}
                        </Badge>
                      ))}
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-3 md:grid-cols-2">
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">节点ID:</span>
                      <p className="text-sm">{selectedRelationship.sourceNode.id}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">显示名称:</span>
                      <p className="text-sm">{selectedRelationship.sourceNode.label}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 目标节点信息 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center space-x-2">
                    <span>目标节点</span>
                    <div className="flex items-center space-x-1">
                      {selectedRelationship.targetNode.labels.map((label) => (
                        <Badge
                          key={label}
                          variant="secondary"
                          style={{
                            backgroundColor: getNodeColor(label),
                            color: 'white'
                          }}
                          className="text-xs"
                        >
                          {label}
                        </Badge>
                      ))}
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-3 md:grid-cols-2">
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">节点ID:</span>
                      <p className="text-sm">{selectedRelationship.targetNode.id}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">显示名称:</span>
                      <p className="text-sm">{selectedRelationship.targetNode.label}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 关系属性 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">关系属性</CardTitle>
                </CardHeader>
                <CardContent>
                  {Object.keys(selectedRelationship.properties).length > 0 ? (
                    <div className="space-y-3">
                      {Object.entries(selectedRelationship.properties).map(([key, value]) => (
                        <div key={key} className="grid grid-cols-3 gap-3">
                          <span className="text-sm font-medium text-muted-foreground">
                            {key}:
                          </span>
                          <span className="text-sm col-span-2 break-all">
                            {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                          </span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">暂无关系属性</p>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 节点详情弹窗 */}
      <Dialog open={showNodeDetails} onOpenChange={setShowNodeDetails}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <Network className="h-5 w-5" />
                <span>节点详情</span>
              </div>
              {selectedNode && (
                <div className="flex items-center space-x-1">
                  {selectedNode.labels?.map((label: string) => (
                    <Badge
                      key={label}
                      variant="secondary"
                      style={{
                        backgroundColor: getNodeColor(label),
                        color: 'white'
                      }}
                      className="text-xs"
                    >
                      {label}
                    </Badge>
                  ))}
                </div>
              )}
            </DialogTitle>
          </DialogHeader>

          {selectedNode && (
            <div className="space-y-4">
              {/* 节点基本信息 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">基本信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-3 md:grid-cols-2">
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">节点ID:</span>
                      <p className="text-sm">{selectedNode.id}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">显示名称:</span>
                      <p className="text-sm">{selectedNode.label}</p>
                    </div>
                    <div className="md:col-span-2">
                      <span className="text-sm font-medium text-muted-foreground">节点标签:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {selectedNode.labels?.map((label: string) => (
                          <Badge key={label} variant="outline" className="text-xs">
                            {label}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 节点属性 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">节点属性</CardTitle>
                </CardHeader>
                <CardContent>
                  {selectedNode.properties && Object.keys(selectedNode.properties).length > 0 ? (
                    <div className="space-y-3">
                      {Object.entries(selectedNode.properties).map(([key, value]) => (
                        <div key={key} className="grid grid-cols-3 gap-3">
                          <span className="text-sm font-medium text-muted-foreground">
                            {key}:
                          </span>
                          <span className="text-sm col-span-2 break-all">
                            {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                          </span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">暂无节点属性</p>
                  )}
                </CardContent>
              </Card>

              {/* 分析结果 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">分析结果</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-3 md:grid-cols-2">
                    {/* 节点度信息 */}
                    {nodeDegreeResults.find(r => r.node.id === selectedNode.id) && (
                      <div>
                        <span className="text-sm font-medium text-muted-foreground">节点度:</span>
                        <p className="text-sm">
                          {nodeDegreeResults.find(r => r.node.id === selectedNode.id)?.degree || 0}
                        </p>
                      </div>
                    )}

                    {/* 中心性信息 */}
                    {centralityResults.find(r => r.node.id === selectedNode.id) && (
                      <div>
                        <span className="text-sm font-medium text-muted-foreground">中心性:</span>
                        <p className="text-sm">
                          {(() => {
                            const result = centralityResults.find(r => r.node.id === selectedNode.id)
                            if (result?.pageRank) return `PageRank: ${result.pageRank.toFixed(4)}`
                            if (result?.betweennessCentrality) return `介数中心性: ${result.betweennessCentrality}`
                            if (result?.closenessCentrality) return `接近中心性: ${result.closenessCentrality.toFixed(4)}`
                            return 'N/A'
                          })()}
                        </p>
                      </div>
                    )}

                    {/* 社区信息 */}
                    {communityResults.find(c => c.nodes.some(n => n.id === selectedNode.id)) && (
                      <div className="md:col-span-2">
                        <span className="text-sm font-medium text-muted-foreground">所属社区:</span>
                        <p className="text-sm">
                          社区 {communityResults.find(c => c.nodes.some(n => n.id === selectedNode.id))?.communityId}
                          ({communityResults.find(c => c.nodes.some(n => n.id === selectedNode.id))?.size} 个节点)
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}