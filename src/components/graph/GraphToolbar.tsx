import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import {
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Download,
  Maximize2,
  Network,
  Shuffle
} from 'lucide-react'
import { LayoutType } from './types'

interface GraphToolbarProps {
  onZoomIn: () => void
  onZoomOut: () => void
  onReset: () => void
  onAutoLayout: () => void
  onExport: () => void
  onFullscreen: () => void
  currentLayout: LayoutType
  onLayoutChange: (layout: LayoutType) => void
  showSidebar: boolean
}

export function GraphToolbar({
  onZoomIn,
  onZoomOut,
  onReset,
  onAutoLayout,
  onExport,
  onFullscreen,
  currentLayout,
  onLayoutChange,
  showSidebar
}: GraphToolbarProps) {
  const [showLayoutMenu, setShowLayoutMenu] = useState(false)

  const layoutOptions = [
    { value: 'fcose', label: 'FCose' },
    { value: 'cola', label: 'Cola' },
    { value: 'dagre', label: 'Dagre' },
    { value: 'circle', label: '圆形' },
    { value: 'grid', label: '网格' }
  ] as const

  return (
    <div 
      className={`absolute bottom-4 z-10 transition-all duration-300 ${
        showSidebar ? 'right-4' : 'right-4'
      }`}
    >
      <Card className="bg-white/95 dark:bg-gray-800/95 backdrop-blur border shadow-lg">
        <CardContent className="p-1.5">
          <div className="flex flex-col space-y-1">
            <Button variant="ghost" size="sm" onClick={onZoomIn} title="放大" className="h-8 w-8 p-0">
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onZoomOut} title="缩小" className="h-8 w-8 p-0">
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onReset} title="重置视图 (空格键)" className="h-8 w-8 p-0">
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onAutoLayout} title="自动布局" className="h-8 w-8 p-0">
              <Shuffle className="h-4 w-4" />
            </Button>
            <div className="h-px w-full bg-gray-300 dark:bg-gray-600 my-1" />
            <Button variant="ghost" size="sm" onClick={onFullscreen} title="全屏" className="h-8 w-8 p-0">
              <Maximize2 className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onExport} title="导出图片" className="h-8 w-8 p-0">
              <Download className="h-4 w-4" />
            </Button>
            <div className="h-px w-full bg-gray-300 dark:bg-gray-600 my-1" />
            
            {/* 布局切换 */}
            <div className="relative">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setShowLayoutMenu(!showLayoutMenu)}
                title={`当前布局: ${currentLayout.toUpperCase()}`}
                className="h-8 w-8 p-0"
              >
                <Network className="h-4 w-4" />
              </Button>
              
              {showLayoutMenu && (
                <div className="absolute bottom-full right-0 mb-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded shadow-lg z-50 min-w-20">
                  {layoutOptions.map((layout) => (
                    <button
                      key={layout.value}
                      onClick={() => {
                        onLayoutChange(layout.value as LayoutType)
                        setShowLayoutMenu(false)
                      }}
                      className={`w-full px-3 py-1.5 text-xs text-left hover:bg-gray-100 dark:hover:bg-gray-700 first:rounded-t last:rounded-b ${
                        currentLayout === layout.value ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : ''
                      }`}
                    >
                      {layout.label}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 