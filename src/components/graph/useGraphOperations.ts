import { useCallback } from 'react'
import { LayoutType, GraphData } from './types'
import { getOptimalConfig, getLayoutConfig } from './utils'

interface UseGraphOperationsProps {
  cyInstanceRef: React.MutableRefObject<any>
  data: GraphData
  config?: any
  onClearHover?: () => void
  onSelectedElementChange?: (element: any) => void
}

export function useGraphOperations({
  cyInstanceRef,
  data,
  config,
  onClearHover,
  onSelectedElementChange
}: UseGraphOperationsProps) {
  
  const handleZoomIn = useCallback(() => {
    const cy = cyInstanceRef.current
    if (cy && !cy.destroyed()) {
      try {
        const zoom = cy.zoom()
        cy.zoom({
          level: zoom * 1.3,
          renderedPosition: { x: cy.width() / 2, y: cy.height() / 2 }
        })
      } catch (error) {
        console.warn('缩放操作失败:', error)
      }
    }
  }, [cyInstanceRef])

  const handleZoomOut = useCallback(() => {
    const cy = cyInstanceRef.current
    if (cy && !cy.destroyed()) {
      try {
        const zoom = cy.zoom()
        cy.zoom({
          level: zoom * 0.7,
          renderedPosition: { x: cy.width() / 2, y: cy.height() / 2 }
        })
      } catch (error) {
        console.warn('缩放操作失败:', error)
      }
    }
  }, [cyInstanceRef])

  const handleReset = useCallback(() => {
    const cy = cyInstanceRef.current
    if (cy && !cy.destroyed()) {
      try {
        cy.fit(undefined, 50)
        cy.elements().removeClass('highlighted selected')
        onSelectedElementChange?.(null)
        onClearHover?.()
      } catch (error) {
        console.warn('重置操作失败:', error)
      }
    }
  }, [cyInstanceRef, onClearHover, onSelectedElementChange])

  const handleLayoutChange = useCallback((layout: LayoutType) => {
    const cy = cyInstanceRef.current
    if (cy && !cy.destroyed()) {
      try {
        const optimalConfig = getOptimalConfig(data.nodes.length, data.edges.length)
        const finalConfig = { ...optimalConfig, ...config }
        const layoutConfig = getLayoutConfig(layout, finalConfig, data)
        cy.layout(layoutConfig).run()
      } catch (error) {
        console.warn('布局切换失败:', error)
      }
    }
  }, [cyInstanceRef, data, config])

  const handleAutoLayout = useCallback(() => {
    const cy = cyInstanceRef.current
    if (cy && !cy.destroyed()) {
      try {
        console.log('🔄 执行自动布局，重新排列所有节点')

        // 清除所有节点的锁定位置
        cy.nodes().forEach((node: any) => {
          node.removeData('lockedPosition')
        })

        // 获取当前最优配置
        const optimalConfig = getOptimalConfig(data.nodes.length, data.edges.length)
        const finalConfig = { ...optimalConfig, ...config }

        // 使用 fcose 布局进行自动重排（通常效果最好）
        const layoutConfig = getLayoutConfig('fcose', finalConfig, data)

        // 执行布局
        const layout = cy.layout(layoutConfig)
        layout.run()

        // 布局完成后适配视图
        layout.on('layoutstop', () => {
          cy.fit(undefined, 50)
          console.log('✅ 自动布局完成')
        })

      } catch (error) {
        console.warn('自动布局失败:', error)
      }
    }
  }, [cyInstanceRef, data, config])

  const handleExport = useCallback(() => {
    const cy = cyInstanceRef.current
    if (cy && !cy.destroyed()) {
      try {
        const png = cy.png({
          output: 'blob',
          bg: '#FFFFFF',
          full: true,
          scale: data.nodes.length > 200 ? 1 : 2 // 大量节点时降低导出质量
        })
        const url = URL.createObjectURL(png)
        const a = document.createElement('a')
        a.href = url
        a.download = `graph-${new Date().toISOString().split('T')[0]}.png`
        a.click()
        URL.revokeObjectURL(url)
      } catch (error) {
        console.warn('导出操作失败:', error)
      }
    }
  }, [cyInstanceRef, data.nodes.length])

  const handleFullscreen = useCallback((containerRef: React.RefObject<HTMLDivElement>) => {
    if (containerRef.current) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen()
      }
    }
  }, [])

  return {
    handleZoomIn,
    handleZoomOut,
    handleReset,
    handleLayoutChange,
    handleAutoLayout,
    handleExport,
    handleFullscreen
  }
} 