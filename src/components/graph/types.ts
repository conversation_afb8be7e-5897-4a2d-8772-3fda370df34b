export interface GraphNode {
  id: string
  label: string
  type: string
  labels?: string[] // 保存原始标签数组
  properties: Record<string, any>
}

export interface GraphEdge {
  id: string
  source: string
  target: string
  label: string
  type: string
  properties: Record<string, any>
  hasOriginalId?: boolean // 标记是否有原始ID，用于判断是否显示ElementId
}

export interface GraphData {
  nodes: GraphNode[]
  edges: GraphEdge[]
}

export interface NodeTypeInfo {
  label: string
  count: number
  color: string
}

export interface RelationshipTypeInfo {
  type: string
  count: number
}

export interface SelectedElement {
  type: 'node' | 'edge'
  data: GraphNode | GraphEdge
}

export interface GraphConfig {
  nodeSize?: number
  fontSize?: number
  edgeWidth?: number
  colorPalette?: string[]
  layout?: {
    nodeSpacing?: number
    edgeLength?: number
    animationDuration?: number
  }
}

export interface GraphVisualizationProps {
  data: GraphData
  onNodeHover?: (node: GraphNode) => void
  onEdgeHover?: (edge: GraphEdge) => void
  onClearHover?: () => void
  onNodeSelect?: (node: GraphNode) => void
  onEdgeSelect?: (edge: GraphEdge) => void
  onNodeDoubleClick?: (node: GraphNode) => void
  hoveredElement?: SelectedElement | null
  nodeTypes?: NodeTypeInfo[]
  relationshipTypes?: RelationshipTypeInfo[]
  config?: GraphConfig
}

export type LayoutType = 'cola' | 'dagre' | 'fcose' | 'circle' | 'grid' 