import { But<PERSON> } from '@/components/ui/button'
import { 
  ChevronRight,
  ChevronLeft,
  BarChart3,
  Tag,
  GitBranch
} from 'lucide-react'
import { GraphData, SelectedElement, GraphNode, GraphEdge } from './types'

interface GraphSidebarProps {
  showSidebar: boolean
  onToggleSidebar: (show: boolean) => void
  displayElement: SelectedElement | null
  data: GraphData
  nodeTypeColors: Record<string, string>
}

export function GraphSidebar({
  showSidebar,
  onToggleSidebar,
  displayElement,
  data,
  nodeTypeColors
}: GraphSidebarProps) {
  if (!showSidebar) {
    return (
      <div className="absolute top-4 right-4 z-10">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => onToggleSidebar(true)}
          className="h-8 w-8 p-0 bg-white/95 dark:bg-gray-800/95 backdrop-blur border shadow-lg"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
      </div>
    )
  }

  return (
    <div 
      className="absolute top-0 right-0 h-full bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700 shadow-lg z-20 transition-transform duration-300 translate-x-0"
      style={{ width: '320px' }}
    >
      {/* 面板头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          {displayElement ? (
            <>
              {displayElement.type === 'node' ? (
                <Tag className="h-4 w-4 text-blue-600 flex-shrink-0" />
              ) : (
                <GitBranch className="h-4 w-4 text-green-600 flex-shrink-0" />
              )}
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
                {/* 对于节点，优先显示 properties 中的名称，备用显示 label */}
                {displayElement.type === 'node' 
                  ? (displayElement.data.properties?.name || 
                     displayElement.data.properties?.title || 
                     displayElement.data.properties?.policy_code || 
                     displayElement.data.properties?.code || 
                     displayElement.data.label)
                  : displayElement.data.label
                }
              </h3>
            </>
          ) : (
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              概览
            </h3>
          )}
        </div>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={() => onToggleSidebar(false)}
          className="h-8 w-8 p-0"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* 面板内容 */}
      <div className="p-4 space-y-6 overflow-y-auto" style={{ height: 'calc(100% - 73px)' }}>
        {displayElement ? (
          <ElementDetails 
            element={displayElement} 
            nodeTypeColors={nodeTypeColors} 
          />
        ) : (
          <GraphOverview 
            data={data} 
            nodeTypeColors={nodeTypeColors} 
          />
        )}
      </div>
    </div>
  )
}

function ElementDetails({ 
  element, 
  nodeTypeColors 
}: { 
  element: SelectedElement, 
  nodeTypeColors: Record<string, string> 
}) {
  return (
    <div className="space-y-4">
      {element.type === 'node' ? (
        <NodeDetails node={element.data as GraphNode} nodeTypeColors={nodeTypeColors} />
      ) : (
        <EdgeDetails edge={element.data as GraphEdge} />
      )}
    </div>
  )
}

function NodeDetails({
  node,
  nodeTypeColors
}: {
  node: GraphNode,
  nodeTypeColors: Record<string, string>
}) {

  return (
    <>
      {/* 节点标签 */}
      <div>
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          节点标签
          <span className="text-xs text-gray-500 ml-2">
            (共 {(node.labels?.length || 0)} 个)
          </span>
        </div>
        <div className="flex flex-wrap gap-1">
          {(node.labels && node.labels.length > 0) ? (
            node.labels.map((label: string, index: number) => (
              <div
                key={index}
                className="inline-flex items-center px-2 py-1 rounded text-xs font-medium"
                style={{
                  backgroundColor: nodeTypeColors[label] || '#8993A4',
                  color: 'white'
                }}
              >
                {label}
              </div>
            ))
          ) : (
            <div
              className="inline-flex items-center px-2 py-1 rounded text-xs font-medium"
              style={{
                backgroundColor: nodeTypeColors[node.type] || '#8993A4',
                color: 'white'
              }}
            >
              {node.type}
            </div>
          )}
        </div>
      </div>

      {/* 基本信息 - elementId, code, name */}
      <div>
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">基本信息</div>
        <div className="space-y-2">
          {/* ElementId (ID) */}
          <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">ElementId</span>
            <span className="text-xs text-gray-800 dark:text-gray-200 font-mono">{node.id}</span>
          </div>
          
          {/* Code */}
          {node.properties?.code && (
            <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Code</span>
              <span className="text-xs text-gray-800 dark:text-gray-200">{node.properties.code}</span>
            </div>
          )}
        </div>
      </div>

      {/* 其他属性信息 */}
      <PropertiesSection properties={node.properties} excludeKeys={['code', 'name']} />
    </>
  )
}

function EdgeDetails({ edge }: { edge: GraphEdge }) {


  return (
    <>
      {/* 关系类型 */}
      <div>
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">关系类型</div>
        <div className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600">
          {edge.type}
        </div>
      </div>

      {/* 基本信息 - elementId, source, target */}
      <div>
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">基本信息</div>
        <div className="space-y-2">
          {/* ElementId (ID) - 只在有原始ID时显示 */}
          {edge.hasOriginalId && (
            <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">ElementId</span>
              <span className="text-xs text-gray-800 dark:text-gray-200 font-mono">{edge.id}</span>
            </div>
          )}
          
          {/* Source */}
          <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">起始节点</span>
            <span className="text-xs text-gray-800 dark:text-gray-200">{edge.source}</span>
          </div>
          
          {/* Target */}
          <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">目标节点</span>
            <span className="text-xs text-gray-800 dark:text-gray-200">{edge.target}</span>
          </div>
        </div>
      </div>

      {/* 其他属性信息 */}
      <PropertiesSection properties={edge.properties} />
    </>
  )
}

function GraphOverview({ 
  data, 
  nodeTypeColors 
}: { 
  data: GraphData, 
  nodeTypeColors: Record<string, string> 
}) {
  return (
    <>
      {/* 节点标签 */}
      <div>
        <div className="flex items-center space-x-2 mb-3">
          <Tag className="h-4 w-4 text-blue-600" />
          <h4 className="font-medium text-gray-900 dark:text-gray-100">节点标签</h4>
        </div>
        <div className="flex flex-wrap gap-2">
          {(() => {
            // 收集所有节点的所有标签
            const allLabels: string[] = []
            data.nodes.forEach(node => {
              if (node.labels && node.labels.length > 0) {
                // 如果节点有labels数组，添加所有标签
                node.labels.forEach(label => {
                  if (!allLabels.includes(label)) {
                    allLabels.push(label)
                  }
                })
              } else if (node.type && !allLabels.includes(node.type)) {
                // 如果没有labels数组，使用type作为备用
                allLabels.push(node.type)
              }
            })

            return allLabels.map((label) => {
              // 统计每个标签的节点数量
              const count = data.nodes.filter(node => {
                if (node.labels && node.labels.length > 0) {
                  return node.labels.includes(label)
                } else {
                  return node.type === label
                }
              }).length

              return (
                <div
                  key={label}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border"
                  style={{
                    backgroundColor: nodeTypeColors[label] || '#8993A4',
                    color: 'white',
                    borderColor: nodeTypeColors[label] || '#8993A4'
                  }}
                >
                  {label} ({count})
                </div>
              )
            })
          })()}
        </div>
      </div>

      {/* 关系类型 */}
      <div>
        <div className="flex items-center space-x-2 mb-3">
          <GitBranch className="h-4 w-4 text-green-600" />
          <h4 className="font-medium text-gray-900 dark:text-gray-100">关系类型</h4>
        </div>
        <div className="flex flex-wrap gap-2">
          {(() => {
            const relationshipCounts: Record<string, number> = {}
            data.edges.forEach(edge => {
              relationshipCounts[edge.type] = (relationshipCounts[edge.type] || 0) + 1
            })

            return Object.entries(relationshipCounts).map(([type, count]) => (
              <div
                key={type}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-300"
              >
                {type} ({count})
              </div>
            ))
          })()}
        </div>
      </div>

      {/* 统计摘要 */}
      <div>
        <div className="flex items-center space-x-2 mb-3">
          <BarChart3 className="h-4 w-4 text-purple-600" />
          <h4 className="font-medium text-gray-900 dark:text-gray-100">统计摘要</h4>
        </div>
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded">
            <div className="text-lg font-bold text-blue-700 dark:text-blue-300">{data.nodes.length}</div>
            <div className="text-xs text-blue-600 dark:text-blue-400">节点总数</div>
          </div>
          <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded">
            <div className="text-lg font-bold text-green-700 dark:text-green-300">{data.edges.length}</div>
            <div className="text-xs text-green-600 dark:text-green-400">关系总数</div>
          </div>
        </div>
      </div>
    </>
  )
}

function PropertiesSection({
  properties,
  excludeKeys = []
}: {
  properties: Record<string, any>,
  excludeKeys?: string[]
}) {
  const filteredProperties = Object.entries(properties || {})
    .filter(([key]) => !excludeKeys.includes(key))

  return (
    <div>
      <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
        其他属性
        <span className="text-xs text-gray-500 ml-2">
          (共 {filteredProperties.length} 个)
        </span>
      </div>
      {filteredProperties.length > 0 ? (
        <div className="space-y-1 max-h-70 overflow-y-auto">
          {filteredProperties.map(([key, value]) => (
            <div key={key} className="flex items-start justify-between p-2 bg-white dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400 mr-2 flex-shrink-0">{key}</span>
              <span className="text-xs text-gray-800 dark:text-gray-200 text-right break-words">
                {typeof value === 'object' && value !== null ? (
                  <pre className="whitespace-pre-wrap font-mono text-xs">
                    {JSON.stringify(value, null, 2)}
                  </pre>
                ) : (
                  <span>
                    {value === null ? 'null' : value === undefined ? 'undefined' : String(value)}
                  </span>
                )}
              </span>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 p-3 rounded text-center">
          暂无{excludeKeys.length > 0 ? '其他' : ''}属性{excludeKeys.length > 0 ? '信息' : ''}
        </div>
      )}
    </div>
  )
} 