import { useEffect, useRef } from 'react'
import cytoscape from 'cytoscape'
import cola from 'cytoscape-cola'
import dagre from 'cytoscape-dagre'
import fcose from 'cytoscape-fcose'
import { GraphData, GraphNode, GraphEdge, SelectedElement, LayoutType } from './types'
import { getOptimalConfig, getLayoutConfig, validateEdges, calculateNewNodePositions, getLeafNeighbors, calculateOffset, applyOffset, detectCollision, findNonCollidingPosition, shouldReLayoutLeafNodes, calculateOptimalLeafPositions, canRestoreEdgeLength, restoreLeafNodePositions } from './utils'
import { CustomMinimap } from './CustomMinimap'

// 注册布局扩展
;(cytoscape as any).use(cola)
;(cytoscape as any).use(dagre)
;(cytoscape as any).use(fcose)

// 自定义触摸板行为设置
function setupCustomTouchpadBehavior(cy: any): (() => void) | null {
  const container = cy.container()
  if (!container) return null

  // 禁用默认的wheel事件处理
  cy.userZoomingEnabled(false)

  // 添加自定义wheel事件监听器
  const handleWheel = (event: WheelEvent) => {
    event.preventDefault()

    // 检测是否按住Command键（Mac）或Ctrl键（Windows/Linux）
    const isZoomModifier = event.metaKey || event.ctrlKey

    if (isZoomModifier) {
      // Command/Ctrl + 滚轮 = 缩放
      // 调整缩放灵敏度，让缩放更平滑
      const zoomSensitivity = 0.1
      const zoomDelta = -event.deltaY * zoomSensitivity * 0.01
      const newZoom = Math.max(0.1, Math.min(5, cy.zoom() * (1 + zoomDelta)))

      const renderedPosition = {
        x: event.clientX - container.getBoundingClientRect().left,
        y: event.clientY - container.getBoundingClientRect().top
      }

      cy.zoom({
        level: newZoom,
        renderedPosition
      })
    } else {
      // 普通滚轮 = 平移
      // 调整平移速度，让移动更自然
      const panSpeed = 1.5
      const deltaX = event.deltaX * panSpeed
      const deltaY = event.deltaY * panSpeed

      const currentPan = cy.pan()
      cy.pan({
        x: currentPan.x - deltaX,
        y: currentPan.y - deltaY
      })
    }
  }

  // 添加事件监听器
  container.addEventListener('wheel', handleWheel, { passive: false })

  // 清理函数（当组件卸载时调用）
  return () => {
    container.removeEventListener('wheel', handleWheel)
  }
}

interface GraphCanvasProps {
  data: GraphData
  nodeTypeColors: Record<string, string>
  currentLayout: LayoutType
  onNodeHover?: (node: GraphNode) => void
  onEdgeHover?: (edge: GraphEdge) => void
  onClearHover?: () => void
  onNodeSelect?: (node: GraphNode) => void
  onEdgeSelect?: (edge: GraphEdge) => void
  onNodeDoubleClick?: (node: GraphNode) => void
  onSelectedElementChange?: (element: SelectedElement | null) => void
  config?: any
  cyInstanceRef: React.MutableRefObject<any>
}

export function GraphCanvas({
  data,
  nodeTypeColors,
  currentLayout,
  onNodeHover,
  onEdgeHover,
  onClearHover,
  onNodeSelect,
  onEdgeSelect,
  onNodeDoubleClick,
  onSelectedElementChange,
  config,
  cyInstanceRef
}: GraphCanvasProps) {
  const canvasRef = useRef<HTMLDivElement>(null)
  
  // 使用ref保存最新的回调函数，避免useEffect重复执行
  const callbacksRef = useRef({
    onNodeHover,
    onEdgeHover,
    onClearHover,
    onNodeSelect,
    onEdgeSelect,
    onNodeDoubleClick,
    onSelectedElementChange
  })
  
  // 更新回调函数引用
  callbacksRef.current = {
    onNodeHover,
    onEdgeHover,
    onClearHover,
    onNodeSelect,
    onEdgeSelect,
    onNodeDoubleClick,
    onSelectedElementChange
  }
  
  // 使用自定义hook处理节点单击/双击事件（暂时注释掉，因为我们已经在useEffect中直接处理事件）
  // const { handleNodeTap, handleNodeDoubleClick, cleanup } = useCytoscapeNodeHandler(
  //   onNodeHover,
  //   onNodeDoubleClick,
  //   250 // 延迟时间
  // )

  // 跟踪之前的数据以检测增量变化
  const prevDataRef = useRef<{ nodes: GraphNode[], edges: GraphEdge[] }>({ nodes: [], edges: [] })
  const isInitializedRef = useRef(false)
  const isUpdatingRef = useRef(false)
  const initialConfigRef = useRef<any>(null)
  const touchpadCleanupRef = useRef<(() => void) | null>(null)
  const lastDoubleClickedNodeRef = useRef<string | null>(null)

  // 拖动状态管理
  const dragStateRef = useRef<{
    isDragging: boolean
    draggedNodeId: string | null
    draggedNodeStartPos: { x: number, y: number } | null
    draggedNodeLastPos: { x: number, y: number } | null
    neighborNodes: string[]
    neighborStartPositions: Record<string, { x: number, y: number }>
  }>({
    isDragging: false,
    draggedNodeId: null,
    draggedNodeStartPos: null,
    draggedNodeLastPos: null,
    neighborNodes: [],
    neighborStartPositions: {}
  })

  // 组件卸载时清理Cytoscape实例
  useEffect(() => {
    return () => {
      // 清理Cytoscape实例
      if (cyInstanceRef.current) {
        try {
          cyInstanceRef.current.destroy()
        } catch (error) {
          console.warn('组件卸载时清理Cytoscape实例失败:', error)
        }
        cyInstanceRef.current = null
      }
    }
  }, [cyInstanceRef])

  useEffect(() => {
    if (!canvasRef.current || !data.nodes.length) return

    // 如果已经初始化且可以进行增量更新，则跳过重新创建
    if (isInitializedRef.current && cyInstanceRef.current) {
      console.log('跳过重新创建，等待增量更新处理')
      return
    }



    // 完全重新创建实例（初始加载或重大变化）
    if (cyInstanceRef.current) {
      console.log('销毁现有实例以创建新实例')
      try {
        cyInstanceRef.current.destroy()
      } catch (error) {
        console.warn('销毁Cytoscape实例时出错:', error)
      }
      cyInstanceRef.current = null
    }

    // 获取动态配置
    const optimalConfig = getOptimalConfig(data.nodes.length, data.edges.length)
    const finalConfig = { ...optimalConfig, ...config }

    // 保存初始配置，用于后续增量更新
    initialConfigRef.current = finalConfig

    // 创建节点ID集合用于验证边的有效性
    const nodeIds = new Set(data.nodes.map(node => node.id))

    // 过滤有效的边 - 确保source和target节点都存在
    const validEdges = validateEdges(data.edges, nodeIds)

    // 转换数据格式
    const nodeCount = data.nodes.length
    const elements = [
      ...data.nodes.map((node, idx) => {
        // 让每个节点初始分布在一个圆环上，保证每次同样数据分布一致
        const angle = (2 * Math.PI * idx) / nodeCount
        const radius = 200 + nodeCount * 2 // 半径可调
        const x = Math.round(Math.cos(angle) * radius)
        const y = Math.round(Math.sin(angle) * radius)
        return {
          data: {
            id: node.id,
            label: node.label,
            type: node.type,
            labels: node.labels, // 保留完整的labels数组
            properties: node.properties, // 保留完整的properties对象
            ...node.properties // 同时展开properties以便Cytoscape样式引用
          },
          position: { x, y }
        }
      }),
      ...validEdges.map(edge => ({
        data: {
          id: edge.id,
          source: edge.source,
          target: edge.target,
          label: edge.label,
          type: edge.type,
          properties: edge.properties, // 保留完整的properties对象
          ...edge.properties // 同时展开properties以便Cytoscape样式引用
        }
      }))
    ]

    // 初始化 Cytoscape
    const cy = (cytoscape as any)({
      container: canvasRef.current,
      elements,
      // 明确启用用户交互
      userZoomingEnabled: true,
      userPanningEnabled: true,
      boxSelectionEnabled: false,
      selectionType: 'single',
      style: [
        {
          selector: 'node',
          style: {
            'background-color': (ele: any) => {
              const labels = ele.data('labels') || []
              const type = ele.data('type')

              // 优先使用第一个有颜色映射的标签
              if (Array.isArray(labels) && labels.length > 0) {
                for (const label of labels) {
                  if (nodeTypeColors[label]) {
                    return nodeTypeColors[label]
                  }
                }
              }

              // 如果没有找到标签颜色，使用type
              return nodeTypeColors[type] || '#8993A4'
            },
            'label': 'data(label)',
            'color': '#FFFFFF',
            'text-outline-color': '#000000',
            'text-outline-width': 2,
            'font-size': `${finalConfig.fontSize}px`,
            'font-weight': 'bold',
            'width': finalConfig.nodeSize,
            'height': finalConfig.nodeSize,
            'text-valign': 'center',
            'text-halign': 'center',
            'text-wrap': 'ellipsis',
            'text-max-width': `${finalConfig.nodeSize * 2}px`,
            'border-width': 3,
            'border-color': '#FFFFFF',
            'border-opacity': 0.8,
            'overlay-padding': 6,
            'z-index': 10
          }
        },
        {
          selector: 'node.selected',
          style: {
            'border-width': 6,
            'border-color': '#007ACC',
            'border-opacity': 1,
            'z-index': 20
          }
        },
        {
          selector: 'edge',
          style: {
            'width': finalConfig.edgeWidth,
            'line-color': '#B0BEC5',
            'target-arrow-color': '#B0BEC5',
            'target-arrow-shape': 'triangle',
            'curve-style': 'bezier',
            'arrow-scale': 1,
            'label': data.nodes.length > 200 ? '' : 'data(label)', // 大量节点时隐藏边标签
            'font-size': `${Math.max(6, finalConfig.fontSize - 4)}px`,
            'color': '#546E7A',
            'text-rotation': 'autorotate',
            'text-margin-y': -10,
            'text-background-color': '#FFFFFF',
            'text-background-opacity': 0.9,
            'text-background-padding': '2px',
            'text-border-color': '#E0E0E0',
            'text-border-width': 0.5,
            'text-border-opacity': 0.7,
            'z-index': 1,
            'opacity': 0.8
          }
        },
        {
          selector: 'edge.selected',
          style: {
            'line-color': '#007ACC',
            'target-arrow-color': '#007ACC',
            'width': finalConfig.edgeWidth * 2,
            'z-index': 15,
            'opacity': 1
          }
        },
        {
          selector: 'node.highlighted',
          style: {
            'border-width': 4,
            'border-color': '#FFF',
            'border-opacity': 1,
            'z-index': 20,
            'opacity': 1
          }
        },
        {
          selector: 'edge.highlighted',
          style: {
            'line-color': '#1976D2',
            'target-arrow-color': '#1976D2',
            'width': finalConfig.edgeWidth * 1.5,
            'z-index': 15,
            'opacity': 1
          }
        },
        {
          selector: '.dimmed',
          style: {
            'opacity': 0.3
          }
        }
      ],
      layout: getLayoutConfig(currentLayout, finalConfig, data),
      minZoom: 0.1,
      maxZoom: 10
    })

    // 明确启用节点拖动功能
    cy.nodes().grabify() // 确保节点可以被拖动

    // tap 事件用于选中
    cy.on('tap', 'node', (evt: any) => {
      if (!cyInstanceRef.current) return
      const node = evt.target
      
      // 立即清除所有选中样式
      cyInstanceRef.current.elements().removeClass('selected')
      
      // 立即添加选中样式
      node.addClass('selected')
      
      // 设置选中状态
      const nodeData = node.data()

      const selectedElement = { type: 'node' as const, data: nodeData }
      callbacksRef.current.onSelectedElementChange?.(selectedElement)
      callbacksRef.current.onNodeSelect?.(nodeData)
    })

    cy.on('tap', 'edge', (evt: any) => {
      if (!cyInstanceRef.current) return
      const edge = evt.target

      // 立即清除所有选中样式
      cyInstanceRef.current.elements().removeClass('selected')

      // 立即添加选中样式
      edge.addClass('selected')

      // 设置选中状态
      const edgeData = edge.data()



      const selectedElement = { type: 'edge' as const, data: edgeData }
      callbacksRef.current.onSelectedElementChange?.(selectedElement)
      callbacksRef.current.onEdgeSelect?.(edgeData)
    })

    // 点击空白区域清除选择
    cy.on('tap', (evt: any) => {
      if (!cyInstanceRef.current) return

      if (evt.target === cy) {
        cyInstanceRef.current.elements().removeClass('selected')
        callbacksRef.current.onSelectedElementChange?.(null)
        // 同时清除悬浮状态，确保显示概览
        callbacksRef.current.onClearHover?.()
      }
    })

    // 保留双击节点加载下级节点逻辑
    cy.on('dblclick', 'node', (evt: any) => {
      if (!cyInstanceRef.current) return
      const node = evt.target

      // 在双击时锁定当前节点位置，防止后续更新影响其位置
      const currentPosition = node.position()
      node.data('lockedPosition', currentPosition)

      // 记录被双击的节点ID，用于新节点定位
      lastDoubleClickedNodeRef.current = node.id()


      callbacksRef.current.onNodeDoubleClick?.(node.data())
    })

    // 悬浮节点
    cy.on('mouseover', 'node', (evt: any) => {
      if (!cyInstanceRef.current) return
      const node = evt.target
      callbacksRef.current.onNodeHover?.(node.data())

      // 只在未选中时显示悬浮效果
      if (!node.hasClass('selected')) {
        node.addClass('highlighted')
      }
    })

    // 悬浮边
    cy.on('mouseover', 'edge', (evt: any) => {
      if (!cyInstanceRef.current) return
      const edge = evt.target
      const edgeData = edge.data()



      callbacksRef.current.onEdgeHover?.(edgeData)

      // 只在未选中时显示悬浮效果
      if (!edge.hasClass('selected')) {
        edge.addClass('highlighted')
      }
    })

    // 鼠标离开节点/边时，移除悬浮效果
    cy.on('mouseout', 'node', (evt: any) => {
      if (!cyInstanceRef.current) return
      const node = evt.target

      // 移除悬浮效果，但保留选中效果
      node.removeClass('highlighted')

      // 延迟调用清除悬浮回调，给其他元素响应悬浮的时间
      setTimeout(() => {
        callbacksRef.current.onClearHover?.()
      }, 10)
    })

    cy.on('mouseout', 'edge', (evt: any) => {
      if (!cyInstanceRef.current) return
      const edge = evt.target

      // 移除悬浮效果，但保留选中效果
      edge.removeClass('highlighted')

      // 延迟调用清除悬浮回调，给其他元素响应悬浮的时间
      setTimeout(() => {
        callbacksRef.current.onClearHover?.()
      }, 10)
    })



    // 拖动开始事件 - 使用 grab 事件
    cy.on('grab', 'node', (evt: any) => {
      if (!cyInstanceRef.current) return
      const node = evt.target
      const nodeId = node.id()

      // 记录拖动状态
      const startPos = node.position()

      // 获取叶子节点
      const leafNeighbors = getLeafNeighbors(cy, nodeId)

      // 只移动叶子节点
      const nodesToMove = leafNeighbors

      // 记录所有要移动节点的初始位置
      const neighborStartPositions: Record<string, { x: number, y: number }> = {}
      nodesToMove.forEach(neighborId => {
        const neighborNode = cy.getElementById(neighborId)
        if (neighborNode.length > 0) {
          const pos = neighborNode.position()
          neighborStartPositions[neighborId] = pos
        }
      })

      dragStateRef.current = {
        isDragging: true,
        draggedNodeId: nodeId,
        draggedNodeStartPos: startPos,
        draggedNodeLastPos: null, // 初始设为null，在第一次position事件时设置
        neighborNodes: nodesToMove,
        neighborStartPositions
      }

      // 只在有叶子节点时输出日志
      if (nodesToMove.length > 0) {

      }
    })

    // 拖动过程中事件 - 使用 position 事件
    cy.on('position', 'node', (evt: any) => {
      const node = evt.target
      const nodeId = node.id()

      if (!cyInstanceRef.current || !dragStateRef.current.isDragging) return

      // 只处理当前被拖动的节点
      if (nodeId !== dragStateRef.current.draggedNodeId) return

      const currentPos = node.position()
      const lastPos = dragStateRef.current.draggedNodeLastPos

      if (!lastPos) {
        // 如果没有上次位置，设置当前位置为上次位置并返回
        dragStateRef.current.draggedNodeLastPos = { x: currentPos.x, y: currentPos.y }
        return
      }

      // 计算增量偏移量（相对于上一次位置的变化）
      const deltaOffset = calculateOffset(lastPos, currentPos)

      // 降低阈值，即使很小的移动也处理
      if (Math.abs(deltaOffset.x) > 0.1 || Math.abs(deltaOffset.y) > 0.1) {
        // 获取所有其他节点的位置（排除被拖动的节点和叶子节点）
        const otherNodePositions: {x: number, y: number}[] = []
        const excludeNodeIds = new Set([dragStateRef.current.draggedNodeId, ...dragStateRef.current.neighborNodes])

        cy.nodes().forEach((node: any) => {
          const nodeId = node.id()
          if (!excludeNodeIds.has(nodeId)) {
            otherNodePositions.push(node.position())
          }
        })

        // 获取当前所有叶子节点的位置
        const currentLeafPositions: {x: number, y: number}[] = []
        dragStateRef.current.neighborNodes.forEach(neighborId => {
          const neighborNode = cy.getElementById(neighborId)
          if (neighborNode.length > 0) {
            currentLeafPositions.push(neighborNode.position())
          }
        })

        // 检查是否需要重新布局（有碰撞风险且边长度不合理）
        const needsReLayout = shouldReLayoutLeafNodes(
          currentPos,
          currentLeafPositions,
          otherNodePositions, // 传入其他节点位置用于碰撞检测
          finalConfig.edgeLength, // 使用动态配置的理想边长度
          0.4, // 容忍度
          60   // 最小安全距离
        )

        if (needsReLayout && dragStateRef.current.neighborNodes.length > 1) {
          // 使用智能重布局
          console.log(`🔄 触发重布局：节点 ${dragStateRef.current.draggedNodeId}，叶子节点数量: ${dragStateRef.current.neighborNodes.length}，目标边长度: ${finalConfig.edgeLength}px`)

          const allConflictPositions = [...otherNodePositions, currentPos] // 包含被拖动节点位置
          const optimalPositions = calculateOptimalLeafPositions(
            currentPos,
            dragStateRef.current.neighborNodes.length,
            allConflictPositions,
            finalConfig.edgeLength, // 使用动态配置的理想距离
            60   // 最小安全距离
          )

          // 应用最佳位置
          dragStateRef.current.neighborNodes.forEach((neighborId, index) => {
            const neighborNode = cy.getElementById(neighborId)
            if (neighborNode.length > 0 && optimalPositions[index]) {
              neighborNode.position(optimalPositions[index])
            }
          })
        } else {
          // 检查是否可以恢复边长度
          const canRestore = canRestoreEdgeLength(
            currentPos,
            currentLeafPositions,
            otherNodePositions,
            finalConfig.edgeLength, // 使用动态配置的理想边长度
            finalConfig.edgeLength * 0.67,  // 最小边长度阈值（理想长度的67%）
            60   // 最小安全距离
          )

          if (canRestore) {
            // 恢复边长度
            console.log(`🔄 恢复边长度：节点 ${dragStateRef.current.draggedNodeId}，叶子节点数量: ${dragStateRef.current.neighborNodes.length}，目标长度: ${finalConfig.edgeLength}px`)

            const restoredPositions = restoreLeafNodePositions(
              currentPos,
              currentLeafPositions,
              otherNodePositions,
              finalConfig.edgeLength, // 使用动态配置的理想边长度
              finalConfig.edgeLength * 0.67,  // 最小边长度阈值（理想长度的67%）
              60   // 最小安全距离
            )

            // 应用恢复后的位置
            let adjustedCount = 0
            dragStateRef.current.neighborNodes.forEach((neighborId, index) => {
              const neighborNode = cy.getElementById(neighborId)
              if (neighborNode.length > 0 && restoredPositions[index]) {
                const originalPos = currentLeafPositions[index]
                const newPos = restoredPositions[index]

                // 检查位置是否发生了变化
                const positionChanged = Math.abs(originalPos.x - newPos.x) > 1 || Math.abs(originalPos.y - newPos.y) > 1
                if (positionChanged) {
                  adjustedCount++
                }

                neighborNode.position(newPos)
              }
            })

            if (adjustedCount > 0) {
              console.log(`✅ 边长度恢复完成，调整了 ${adjustedCount} 个叶子节点位置`)
            }
          } else {
            // 使用精细化的增量移动逻辑：只调整有碰撞的叶子节点
            const adjustedLeafPositions: {x: number, y: number}[] = []
            const finalLeafPositions: {x: number, y: number}[] = []

            dragStateRef.current.neighborNodes.forEach(neighborId => {
              const neighborNode = cy.getElementById(neighborId)

              if (neighborNode.length > 0) {
                const currentNeighborPos = neighborNode.position()
                const idealNewPos = applyOffset(currentNeighborPos, deltaOffset)

                // 检查理想新位置是否有碰撞
                const allConflictPositions = [...otherNodePositions, currentPos, ...adjustedLeafPositions]
                const hasCollision = allConflictPositions.some(pos =>
                  detectCollision(idealNewPos, pos, 60)
                )

                let finalPosition: {x: number, y: number}

                if (hasCollision) {
                  // 只有发生碰撞时才调整位置
                  finalPosition = findNonCollidingPosition(
                    idealNewPos,
                    allConflictPositions,
                    currentPos, // 被拖动节点的当前位置
                    60, // 最小距离
                    12  // 最大尝试次数
                  )
                  adjustedLeafPositions.push(finalPosition)


                } else {
                  // 没有碰撞，使用理想位置（跟随拖动）
                  finalPosition = idealNewPos
                }

                neighborNode.position(finalPosition)
                finalLeafPositions.push(finalPosition)
              }
            })
          }
        }
      }

      // 无论是否移动叶子节点，都要更新上次位置
      dragStateRef.current.draggedNodeLastPos = { x: currentPos.x, y: currentPos.y }
    })

    // 拖动结束事件 - 使用 free 事件
    cy.on('free', 'node', (evt: any) => {
      if (!cyInstanceRef.current) return
      const node = evt.target
      const nodeId = node.id()

      // 只处理当前被拖动的节点
      if (nodeId === dragStateRef.current.draggedNodeId) {
        // 重置拖动状态
        dragStateRef.current = {
          isDragging: false,
          draggedNodeId: null,
          draggedNodeStartPos: null,
          draggedNodeLastPos: null,
          neighborNodes: [],
          neighborStartPositions: {}
        }
      }
    })

    cyInstanceRef.current = cy

    // 自定义触摸板行为
    touchpadCleanupRef.current = setupCustomTouchpadBehavior(cy)

    // 初始化时适配视图
    setTimeout(() => {
      cy.fit(cy.nodes(), 50)
    }, 100)







    // 标记为已初始化
    isInitializedRef.current = true

    // 只在创建新实例时更新缓存数据
    prevDataRef.current = { nodes: [...data.nodes], edges: [...validEdges] }


    return () => {
      // 清理触摸板事件监听器
      if (touchpadCleanupRef.current) {
        touchpadCleanupRef.current()
        touchpadCleanupRef.current = null
      }



      // 不在cleanup中销毁实例，避免因依赖项变化导致意外销毁
      // 实例销毁只在组件卸载时或明确需要重新创建时进行

    }
  }, [currentLayout, config, nodeTypeColors])

  // 专门处理数据变化的useEffect
  useEffect(() => {


    // 防止重复更新
    if (isUpdatingRef.current) {
      console.log('正在更新中，跳过此次数据变化')
      return
    }

    if (!cyInstanceRef.current || !data.nodes.length) {
      // 如果没有实例或没有数据，标记为未初始化
      console.log('没有实例或数据，标记为未初始化')
      isInitializedRef.current = false
      return
    }

    // 在检查增量更新之前先设置更新标记，防止竞争条件
    isUpdatingRef.current = true

    // 使用初始配置保持一致性，避免因节点数量变化导致配置变化
    const finalConfig = initialConfigRef.current || { ...getOptimalConfig(data.nodes.length, data.edges.length), ...config }

    // 立即保存当前的prevData快照，防止被其他useEffect修改
    const currentPrevData = { ...prevDataRef.current }

    // 创建节点ID集合用于验证边的有效性
    const nodeIds = new Set(data.nodes.map(node => node.id))
    const validEdges = validateEdges(data.edges, nodeIds)

    // 检查是否为增量更新，使用快照数据
    const isIncrementalUpdate = isInitializedRef.current &&
      currentPrevData.nodes.length > 0 &&
      data.nodes.length > currentPrevData.nodes.length



    if (isIncrementalUpdate) {


      // 增量更新：只添加新节点和边，保持现有节点位置
      const existingNodeIds = new Set(currentPrevData.nodes.map(n => n.id))
      const existingEdgeIds = new Set(currentPrevData.edges.map(e => e.id))

      const newNodes = data.nodes.filter(n => !existingNodeIds.has(n.id))
      const newEdges = validEdges.filter(e => !existingEdgeIds.has(e.id))

      if (newNodes.length > 0 || newEdges.length > 0) {
        const cy = cyInstanceRef.current

        // 添加新边
        const newEdgeElements = newEdges.map(edge => ({
          data: {
            id: edge.id,
            source: edge.source,
            target: edge.target,
            label: edge.label,
            type: edge.type,
            properties: edge.properties,
            ...edge.properties
          }
        }))

        // 禁用自动布局和动画，确保增量更新不会影响现有节点位置
        cy.autolock(true)
        cy.autoungrabify(true)

        // 先计算新节点的合适位置，传递被双击节点信息
        const newNodePositions = calculateNewNodePositions(
          cy,
          newNodes,
          newEdges,
          finalConfig.nodeSpacing,
          lastDoubleClickedNodeRef.current
        )

        // 在添加元素时就设置正确的位置
        const newNodeElementsWithPositions = newNodes.map(node => ({
          data: {
            id: node.id,
            label: node.label,
            type: node.type,
            labels: node.labels, // 添加缺失的 labels 字段
            properties: node.properties,
            ...node.properties
          },
          position: newNodePositions[node.id] || { x: 0, y: 0 }
        }))

        // 批量添加新元素（带位置信息）
        cy.add([...newNodeElementsWithPositions, ...newEdgeElements])

        // 验证新节点位置（开发调试用）
        // newNodes.forEach(node => {
        //   const cyNode = cy.getElementById(node.id)
        //   if (cyNode.length > 0) {
        //     const actualPos = cyNode.position()
        //     const expectedPos = newNodePositions[node.id]
        //     console.log(`节点 ${node.id}: 期望位置=(${expectedPos.x.toFixed(1)}, ${expectedPos.y.toFixed(1)}), 实际位置=(${actualPos.x.toFixed(1)}, ${actualPos.y.toFixed(1)})`)
        //   }
        // })
        cy.autolock(false)
        cy.autoungrabify(false)

        // 确保所有有锁定位置的节点恢复到原位置
        cy.nodes().forEach((node: any) => {
          const lockedPosition = node.data('lockedPosition')
          if (lockedPosition) {
            node.position(lockedPosition)
            // console.log(`恢复节点 ${node.id()} 到锁定位置:`, lockedPosition)
          }
        })

        // console.log(`增量更新完成：添加了 ${newNodes.length} 个节点，${newEdges.length} 条边`)
      } else {
        // 增量更新：没有新节点或边需要添加，保持当前布局
      }
    } else if (data.nodes.length === currentPrevData.nodes.length) {
      // 数据没有变化，不需要任何操作
    } else {
      // 数据发生重大变化，需要重新初始化图谱
      // 标记需要重新初始化
      isInitializedRef.current = false
    }

    // 更新缓存数据
    prevDataRef.current = { nodes: [...data.nodes], edges: [...validEdges] }

    // 重置更新标记
    isUpdatingRef.current = false
  }, [data, config])

  return (
    <div className="relative w-full h-full">
      <div ref={canvasRef} className="w-full h-full" />
      {cyInstanceRef.current && (
        <CustomMinimap
          cyInstance={cyInstanceRef.current}
          data={data}
          nodeTypeColors={nodeTypeColors}
        />
      )}
    </div>
  )
} 