# GraphVisualization 组件重构说明

## 概述

GraphVisualization 组件已经按照最佳实践重构，将原本的大文件（1113行）拆分为多个模块，提高了代码的可维护性和可测试性。

## 目录结构

```
src/components/graph/
├── index.ts              # 统一导出文件
├── types.ts              # 类型定义
├── utils.ts              # 工具函数
├── GraphVisualization.tsx # 主组件
├── GraphCanvas.tsx       # 图表画布组件
├── GraphToolbar.tsx      # 工具栏组件
├── GraphSidebar.tsx      # 侧边栏组件
├── useGraphOperations.ts # 图表操作自定义hook
└── README.md             # 说明文档
```

## 模块说明

### 1. types.ts
包含所有的类型定义：
- `GraphNode` - 图表节点类型
- `GraphEdge` - 图表边类型
- `GraphData` - 图表数据类型
- `SelectedElement` - 选中元素类型
- `GraphConfig` - 图表配置类型
- `GraphVisualizationProps` - 主组件属性类型
- `LayoutType` - 布局类型

### 2. utils.ts
包含工具函数：
- `getOptimalConfig()` - 根据节点数量获取最优配置
- `getLayoutConfig()` - 获取布局配置
- `validateEdges()` - 验证边的有效性

### 3. GraphCanvas.tsx
负责实际的Cytoscape图表渲染：
- 处理Cytoscape实例的创建和销毁
- 管理图表样式和布局
- 处理节点和边的交互事件

### 4. GraphToolbar.tsx
图表工具栏组件：
- 缩放控制（放大/缩小/重置）
- 导出功能
- 全屏功能
- 布局切换

### 5. GraphSidebar.tsx
右侧信息面板：
- 显示选中元素的详细信息
- 显示图表的整体概览
- 节点和边的属性展示

### 6. useGraphOperations.ts
图表操作的自定义hook：
- 提供所有图表操作函数
- 统一管理Cytoscape实例操作
- 错误处理和边界检查

### 7. GraphVisualization.tsx
主组件：
- 组合所有子组件
- 管理组件间的状态传递
- 处理颜色管理和配置

## 使用方式

### 基本使用

```tsx
import { GraphVisualization } from '@/components/graph'

function App() {
  const data = {
    nodes: [
      { id: '1', label: 'Node 1', type: 'type1', properties: {} }
    ],
    edges: [
      { id: 'e1', source: '1', target: '2', label: 'Edge 1', type: 'relation', properties: {} }
    ]
  }

  return (
    <GraphVisualization
      data={data}
      onNodeSelect={(node) => console.log('Selected node:', node)}
      onEdgeSelect={(edge) => console.log('Selected edge:', edge)}
    />
  )
}
```

### 单独使用子组件

```tsx
import { GraphCanvas, GraphToolbar, GraphSidebar } from '@/components/graph'

// 可以单独使用各个子组件来构建自定义的图表界面
```

## 重构收益

1. **模块化**: 每个文件职责单一，便于维护和测试
2. **可重用性**: 子组件可以单独使用和测试
3. **类型安全**: 统一的类型定义，减少类型错误
4. **代码组织**: 清晰的目录结构，便于理解和协作
5. **性能优化**: 更细粒度的组件更新，减少不必要的重渲染

## 迁移指南

如果你在其他地方直接导入了原来的GraphVisualization组件，现在应该从新的入口导入：

```tsx
// 旧的导入方式
import { GraphVisualization } from '@/components/graph/GraphVisualization'

// 新的导入方式（推荐）
import { GraphVisualization } from '@/components/graph'
```

组件的API保持不变，所以现有的使用代码不需要修改。 