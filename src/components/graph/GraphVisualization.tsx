import { useRef, useState, useMemo, useCallback, useEffect } from 'react'
import { Core } from 'cytoscape'
import { getGlobalColorMap } from '@/lib/colorManager'
import { GraphVisualizationProps, SelectedElement, LayoutType } from './types'
import { GraphCanvas } from './GraphCanvas'
import { GraphToolbar } from './GraphToolbar'
import { GraphSidebar } from './GraphSidebar'
import { useGraphOperations } from './useGraphOperations'



export function GraphVisualization({
  data,
  onNodeHover: _onNodeHover,
  onEdgeHover: _onEdgeHover,
  onClearHover,
  onNodeSelect,
  onEdgeSelect,
  onNodeDoubleClick,
  nodeTypes: _nodeTypes,
  relationshipTypes: _relationshipTypes,
  config
}: GraphVisualizationProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const cyInstanceRef = useRef<Core | null>(null)
  const [currentLayout, setCurrentLayout] = useState<LayoutType>('fcose')
  const [showSidebar, setShowSidebar] = useState(true)
  const [selectedElement, setSelectedElement] = useState<SelectedElement | null>(null)
  const [hoveredElement, setHoveredElement] = useState<SelectedElement | null>(null)

  // 使用稳定的颜色映射，避免因新节点导致重新计算
  const nodeTypeColorsRef = useRef<Record<string, string>>({})
  const isColorsInitializedRef = useRef(false)

  // 使用useCallback创建稳定的颜色更新函数
  const updateColors = useCallback(() => {
    const globalColors = getGlobalColorMap()

    // 如果全局颜色映射为空，生成临时颜色映射
    if (Object.keys(globalColors).length === 0) {
      const uniqueTypes = [...new Set(data.nodes.map(node => node.type))]
      const existingColors = nodeTypeColorsRef.current
      const tempColors: Record<string, string> = { ...existingColors }
      const goldenRatio = 0.618033988749895

      // 只为新的节点类型生成颜色，保持现有类型的颜色不变
      let colorIndex = Object.keys(existingColors).length
      let hasNewColors = false

      uniqueTypes.forEach((type) => {
        if (!tempColors[type]) {
          const hue = (colorIndex * goldenRatio * 360) % 360
          const saturation = 65 + (colorIndex % 3) * 10
          const lightness = 50 + (colorIndex % 4) * 10
          tempColors[type] = `hsl(${Math.round(hue)}, ${saturation}%, ${lightness}%)`
          colorIndex++
          hasNewColors = true
        }
      })

      // 只在有新颜色或首次初始化时更新缓存和打印日志
      if (hasNewColors || !isColorsInitializedRef.current) {
        nodeTypeColorsRef.current = tempColors
        isColorsInitializedRef.current = true
        // console.log('⚠️ 全局颜色映射为空，使用临时颜色方案:', tempColors)
      }

      return tempColors
    }

    // 如果全局颜色映射不为空，更新本地缓存
    nodeTypeColorsRef.current = globalColors
    isColorsInitializedRef.current = true
    return globalColors
  }, [data.nodes, config?.colorPalette])

  // 使用稳定的颜色映射，同时监听全局颜色映射的变化
  const nodeTypeColors = useMemo(() => {
    return updateColors()
  }, [updateColors, getGlobalColorMap()])

  // 计算显示元素：悬浮优先，然后选中，最后概览
  const displayElement = hoveredElement || selectedElement

  // 使用图表操作hook
  const {
    handleZoomIn,
    handleZoomOut,
    handleReset,
    handleLayoutChange: baseHandleLayoutChange,
    handleAutoLayout,
    handleExport,
    handleFullscreen
  } = useGraphOperations({
    cyInstanceRef,
    data,
    config,
    onClearHover,
    onSelectedElementChange: setSelectedElement
  })

  // 处理布局变更
  const handleLayoutChange = (layout: LayoutType) => {
    setCurrentLayout(layout)
    baseHandleLayoutChange(layout)
  }

  // 处理全屏
  const handleFullscreenClick = () => {
    handleFullscreen(containerRef)
  }

  // 处理选中元素变更
  const handleSelectedElementChange = (element: SelectedElement | null) => {
    setSelectedElement(element)
  }

  // 添加键盘快捷键监听
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 检查是否在输入框中，如果是则不处理快捷键
      const target = event.target as HTMLElement
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
        return
      }

      // 空格键重置视图
      if (event.code === 'Space') {
        event.preventDefault() // 防止页面滚动
        handleReset()
      }
    }

    // 添加事件监听器
    document.addEventListener('keydown', handleKeyDown)

    // 清理函数
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleReset])

    return (
    <div ref={containerRef} className="relative w-full h-full bg-white dark:bg-gray-900 flex">
      {/* 图谱画布区域 */}
      <div 
        className={`flex-1 relative transition-all duration-300 ${
          showSidebar ? 'mr-80' : 'mr-0'
        }`}
        style={{ width: showSidebar ? 'calc(100% - 320px)' : '100%' }}
      >
        <GraphCanvas
          data={data}
          nodeTypeColors={nodeTypeColors}
          currentLayout={currentLayout}
          onNodeHover={el => setHoveredElement({ type: 'node', data: el })}
          onEdgeHover={el => setHoveredElement({ type: 'edge', data: el })}
          onClearHover={() => setHoveredElement(null)}
          onNodeSelect={onNodeSelect}
          onEdgeSelect={onEdgeSelect}
          onNodeDoubleClick={onNodeDoubleClick}
          onSelectedElementChange={handleSelectedElementChange}
          config={config}
          cyInstanceRef={cyInstanceRef}
        />

        {/* 工具栏 */}
        <GraphToolbar
          onZoomIn={handleZoomIn}
          onZoomOut={handleZoomOut}
          onReset={handleReset}
          onAutoLayout={handleAutoLayout}
          onExport={handleExport}
          onFullscreen={handleFullscreenClick}
          currentLayout={currentLayout}
          onLayoutChange={handleLayoutChange}
          showSidebar={showSidebar}
        />
      </div>

      {/* 侧边栏 */}
      <GraphSidebar
        showSidebar={showSidebar}
        onToggleSidebar={setShowSidebar}
        displayElement={displayElement}
        data={data}
        nodeTypeColors={nodeTypeColors}
      />
    </div>
  )
} 