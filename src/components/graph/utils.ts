import { LayoutType, GraphData } from './types'

// 根据节点数量动态调整参数
export function getOptimalConfig(nodeCount: number, _edgeCount: number) {
  if (nodeCount <= 50) {
    return {
      nodeSize: 60,
      fontSize: 14,
      edgeWidth: 1.5,
      nodeSpacing: 150,
      edgeLength: 180,
      animationDuration: 0
    }
  } else if (nodeCount <= 200) {
    return {
      nodeSize: 45,
      fontSize: 12,
      edgeWidth: 1.2,
      nodeSpacing: 100,
      edgeLength: 150,
      animationDuration: 0
    }
  } else if (nodeCount <= 500) {
    return {
      nodeSize: 35,
      fontSize: 10,
      edgeWidth: 1,
      nodeSpacing: 80,
      edgeLength: 120,
      animationDuration: 0
    }
  } else {
    return {
      nodeSize: 25,
      fontSize: 8,
      edgeWidth: 0.8,
      nodeSpacing: 60,
      edgeLength: 100,
      animationDuration: 0
    }
  }
}

// 获取布局配置
export function getLayoutConfig(layoutType: LayoutType, config: any, data: GraphData) {
  const baseConfig = {
    animate: false, // 禁用所有布局动画
    animationDuration: 0, // 设置动画时长为0
    fit: true,
    padding: Math.max(20, Math.min(50, 500 / data.nodes.length)) // 动态padding
  }

  switch (layoutType) {
    case 'cola':
      return {
        ...baseConfig,
        name: 'cola',
        nodeSpacing: config.nodeSpacing,
        edgeLength: config.edgeLength,
        avoidOverlap: true,
        handleDisconnected: true,
        convergenceThreshold: data.nodes.length > 200 ? 0.1 : 0.01,
        refresh: data.nodes.length > 200 ? 5 : 10,
        maxSimulationTime: Math.min(8000, data.nodes.length * 20)
      }
    case 'dagre':
      return {
        ...baseConfig,
        name: 'dagre',
        rankDir: 'TB',
        nodeSep: config.nodeSpacing,
        edgeSep: Math.max(20, config.nodeSpacing / 2),
        rankSep: config.edgeLength
      }
    case 'fcose':
      return {
        ...baseConfig,
        name: 'fcose',
        quality: data.nodes.length > 200 ? 'draft' : 'default',
        numIter: Math.min(2500, Math.max(100, 5000 / data.nodes.length)),
        sampleSize: Math.min(25, Math.max(5, 50 / data.nodes.length)),
        nodeRepulsion: config.nodeSpacing * 50,
        idealEdgeLength: config.edgeLength,
        edgeElasticity: 0.45,
        nestingFactor: 0.1,
        randomize: false
      }
    case 'circle':
      return {
        ...baseConfig,
        name: 'circle',
        radius: Math.max(100, data.nodes.length * 3),
        spacing: config.nodeSpacing
      }
    case 'grid':
      return {
        ...baseConfig,
        name: 'grid',
        rows: Math.ceil(Math.sqrt(data.nodes.length)),
        spacing: config.nodeSpacing
      }
    default:
      return { ...baseConfig, name: 'fcose' }
  }
}

// 验证边的有效性
export function validateEdges(edges: any[], nodeIds: Set<string>) {
  return edges.filter(edge => {
    const hasValidSource = edge.source && nodeIds.has(edge.source)
    const hasValidTarget = edge.target && nodeIds.has(edge.target)

    if (!hasValidSource || !hasValidTarget) {
      console.warn('过滤无效边:', {
        edge: edge.id,
        source: edge.source,
        target: edge.target,
        hasValidSource,
        hasValidTarget
      })
      return false
    }
    return true
  })
}

// 检查位置是否与现有节点冲突
function isPositionOccupied(
  cy: any,
  x: number,
  y: number,
  minDistance: number,
  excludeNewNodes: string[] = []
): boolean {
  const existingNodes = cy.nodes()
  for (let i = 0; i < existingNodes.length; i++) {
    const node = existingNodes[i]
    const nodeId = node.id()

    // 跳过新添加的节点，避免新节点之间的冲突检测
    if (excludeNewNodes.includes(nodeId)) {
      continue
    }

    const pos = node.position()
    const distance = Math.sqrt(Math.pow(pos.x - x, 2) + Math.pow(pos.y - y, 2))
    if (distance < minDistance) {
      return true
    }
  }
  return false
}

// 找到一个不冲突的位置
function findAvailablePosition(
  cy: any,
  centerX: number,
  centerY: number,
  preferredAngle: number,
  radius: number,
  nodeSpacing: number,
  excludeNewNodes: string[] = [],
  maxAttempts: number = 12
): { x: number, y: number } {
  const minDistance = nodeSpacing * 0.8

  // 首先尝试首选角度
  for (let radiusMultiplier = 1; radiusMultiplier <= 3; radiusMultiplier++) {
    const currentRadius = radius * radiusMultiplier
    const x = centerX + currentRadius * Math.cos(preferredAngle)
    const y = centerY + currentRadius * Math.sin(preferredAngle)

    if (!isPositionOccupied(cy, x, y, minDistance, excludeNewNodes)) {
      return { x, y }
    }
  }

  // 如果首选角度不可用，尝试附近的角度
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    const angleOffset = (attempt % 2 === 0 ? 1 : -1) * (Math.PI / 6) * Math.floor(attempt / 2)
    const angle = preferredAngle + angleOffset

    for (let radiusMultiplier = 1; radiusMultiplier <= 3; radiusMultiplier++) {
      const currentRadius = radius * radiusMultiplier
      const x = centerX + currentRadius * Math.cos(angle)
      const y = centerY + currentRadius * Math.sin(angle)

      if (!isPositionOccupied(cy, x, y, minDistance, excludeNewNodes)) {
        return { x, y }
      }
    }
  }

  // 如果还是找不到，返回首选位置（可能会有轻微重叠）
  return {
    x: centerX + radius * Math.cos(preferredAngle),
    y: centerY + radius * Math.sin(preferredAngle)
  }
}

// 为新节点计算合适的位置
export function calculateNewNodePositions(
  cy: any,
  newNodes: any[],
  newEdges: any[],
  nodeSpacing: number,
  doubleClickedNodeId?: string | null
) {
  const positions: { [nodeId: string]: { x: number, y: number } } = {}

  if (newNodes.length === 0) return positions

  // 找到与新节点相连的现有节点（被双击的节点）
  const connectedExistingNodes = new Set<string>()
  const nodeConnections: { [nodeId: string]: string[] } = {}

  newEdges.forEach(edge => {
    const sourceExists = cy.getElementById(edge.source).length > 0
    const targetExists = cy.getElementById(edge.target).length > 0
    const sourceIsNew = newNodes.find(n => n.id === edge.source)
    const targetIsNew = newNodes.find(n => n.id === edge.target)

    if (sourceExists && !sourceIsNew) {
      connectedExistingNodes.add(edge.source)
      if (targetIsNew) {
        if (!nodeConnections[edge.target]) nodeConnections[edge.target] = []
        nodeConnections[edge.target].push(edge.source)
      }
    }
    if (targetExists && !targetIsNew) {
      connectedExistingNodes.add(edge.target)
      if (sourceIsNew) {
        if (!nodeConnections[edge.source]) nodeConnections[edge.source] = []
        nodeConnections[edge.source].push(edge.target)
      }
    }
  })

  // 优先使用被双击的节点作为中心
  let centerX: number | undefined, centerY: number | undefined, centerNodeId: string | undefined

  if (doubleClickedNodeId) {
    const doubleClickedNode = cy.getElementById(doubleClickedNodeId)
    if (doubleClickedNode.length > 0) {
      const pos = doubleClickedNode.position()
      centerX = pos.x
      centerY = pos.y
      centerNodeId = doubleClickedNodeId
      // console.log(`使用被双击节点 ${doubleClickedNodeId} 作为分布中心: (${centerX!.toFixed(1)}, ${centerY!.toFixed(1)})`)
    }
  }

  // 如果没有被双击节点信息，则使用连接的现有节点
  if (!centerX && connectedExistingNodes.size > 0) {
    // 基于连接的现有节点位置来定位新节点
    const existingPositions = Array.from(connectedExistingNodes).map(nodeId => {
      const node = cy.getElementById(nodeId)
      return { id: nodeId, pos: node.length > 0 ? node.position() : null }
    }).filter(item => item.pos !== null)

    if (existingPositions.length > 0) {
      // 计算中心位置
      if (existingPositions.length === 1) {
        // 如果只有一个连接节点，以它为中心
        centerX = existingPositions[0].pos.x
        centerY = existingPositions[0].pos.y
        centerNodeId = existingPositions[0].id
      } else {
        // 如果有多个连接节点，计算中心位置
        centerX = existingPositions.reduce((sum, item) => sum + item.pos.x, 0) / existingPositions.length
        centerY = existingPositions.reduce((sum, item) => sum + item.pos.y, 0) / existingPositions.length
      }
      console.log(`使用连接节点作为分布中心: (${centerX!.toFixed(1)}, ${centerY!.toFixed(1)})`)
    }
  }

  if (centerX !== undefined && centerY !== undefined) {

    // 智能分布新节点
    const baseRadius = nodeSpacing * 1.8
    const nodesPerLayer = Math.min(8, Math.max(4, newNodes.length)) // 每层最多8个节点

    newNodes.forEach((node, index) => {
      // 确定节点应该在哪一层
      const layer = Math.floor(index / nodesPerLayer)
      const positionInLayer = index % nodesPerLayer
      const nodesInCurrentLayer = Math.min(nodesPerLayer, newNodes.length - layer * nodesPerLayer)

      // 计算当前层的半径
      const currentRadius = baseRadius * (1 + layer * 0.6)

      // 计算基础角度，确保均匀分布
      const baseAngle = (positionInLayer * 2 * Math.PI) / nodesInCurrentLayer

      // 如果节点有特定的连接关系，优先考虑连接节点的方向
      let preferredAngle = baseAngle
      if (nodeConnections[node.id] && nodeConnections[node.id].length > 0) {
        const connectedNodeId = nodeConnections[node.id][0]
        // 如果连接的是中心节点，使用基础角度
        if (connectedNodeId === centerNodeId) {
          preferredAngle = baseAngle
        } else {
          // 如果连接的是其他节点，考虑其方向
          const connectedNode = cy.getElementById(connectedNodeId)
          if (connectedNode.length > 0) {
            const connectedPos = connectedNode.position()
            const connectionAngle = Math.atan2(connectedPos.y - centerY, connectedPos.x - centerX)
            preferredAngle = connectionAngle + (positionInLayer - nodesInCurrentLayer / 2) * (Math.PI / 6)
          }
        }
      }

      // 添加一些随机偏移，避免过于规整
      const angleOffset = (Math.random() - 0.5) * (Math.PI / 12) // ±15度随机偏移
      preferredAngle += angleOffset

      // 直接使用计算出的位置，避免过度的冲突检测导致节点聚集
      const position = {
        x: centerX + currentRadius * Math.cos(preferredAngle),
        y: centerY + currentRadius * Math.sin(preferredAngle)
      }

      // 只在位置明显冲突时才调整（距离中心节点太近）
      const distanceToCenter = Math.sqrt(Math.pow(position.x - centerX, 2) + Math.pow(position.y - centerY, 2))
      if (distanceToCenter < nodeSpacing) {
        position.x = centerX + nodeSpacing * 1.5 * Math.cos(preferredAngle)
        position.y = centerY + nodeSpacing * 1.5 * Math.sin(preferredAngle)
      }

      positions[node.id] = position

      // console.log(`新节点 ${node.id} 定位: 中心节点=${centerNodeId}, 层=${layer}, 角度=${(preferredAngle * 180 / Math.PI).toFixed(1)}°, 位置=(${position.x.toFixed(1)}, ${position.y.toFixed(1)})`)
    })
  } else {
    // 如果没有连接的现有节点，在画布中心附近分布
    const extent = cy.extent()
    const centerX = (extent.x1 + extent.x2) / 2
    const centerY = (extent.y1 + extent.y2) / 2
    const radius = nodeSpacing * 1.5

    const newNodeIds = newNodes.map(n => n.id)
    newNodes.forEach((node, index) => {
      const angle = (index * 2 * Math.PI) / newNodes.length
      const position = findAvailablePosition(cy, centerX, centerY, angle, radius, nodeSpacing, newNodeIds)
      positions[node.id] = position
    })
  }

  return positions
}

/**
 * 查找指定节点的所有一级邻居节点
 * @param cy Cytoscape实例
 * @param nodeId 目标节点ID
 * @returns 一级邻居节点的ID数组
 */
export function getFirstLevelNeighbors(cy: any, nodeId: string): string[] {
  const targetNode = cy.getElementById(nodeId)
  if (!targetNode.length) return []

  // 获取与该节点直接连接的所有边
  const connectedEdges = targetNode.connectedEdges()

  // 获取这些边连接的其他节点
  const neighbors: string[] = []
  connectedEdges.forEach((edge: any) => {
    const sourceId = edge.source().id()
    const targetId = edge.target().id()

    // 添加另一端的节点（不是当前节点的那一端）
    if (sourceId === nodeId && targetId !== nodeId) {
      neighbors.push(targetId)
    } else if (targetId === nodeId && sourceId !== nodeId) {
      neighbors.push(sourceId)
    }
  })

  // 去重并返回
  return [...new Set(neighbors)]
}

/**
 * 查找指定节点的叶子节点（只与该节点连接，没有其他连接的邻居节点）
 * @param cy Cytoscape实例
 * @param nodeId 目标节点ID
 * @returns 叶子节点的ID数组
 */
export function getLeafNeighbors(cy: any, nodeId: string): string[] {
  const neighbors = getFirstLevelNeighbors(cy, nodeId)

  // 过滤出叶子节点：只有一个连接（就是与目标节点的连接）
  const leafNodes = neighbors.filter(neighborId => {
    const neighborNode = cy.getElementById(neighborId)
    if (!neighborNode.length) return false

    // 获取邻居节点的所有连接边数量
    const connectedEdges = neighborNode.connectedEdges()

    // 如果只有一条边，说明是叶子节点
    return connectedEdges.length === 1
  })

  // console.log(`🍃 节点 ${nodeId} 的叶子邻居:`, leafNodes)
  return leafNodes
}

/**
 * 计算两个位置之间的偏移量
 * @param from 起始位置
 * @param to 目标位置
 * @returns 偏移量 {x, y}
 */
export function calculateOffset(from: {x: number, y: number}, to: {x: number, y: number}) {
  return {
    x: to.x - from.x,
    y: to.y - from.y
  }
}

/**
 * 应用偏移量到位置
 * @param position 原始位置
 * @param offset 偏移量
 * @returns 新位置
 */
export function applyOffset(position: {x: number, y: number}, offset: {x: number, y: number}) {
  return {
    x: position.x + offset.x,
    y: position.y + offset.y
  }
}

/**
 * 检测两个节点是否发生碰撞
 * @param pos1 第一个节点位置
 * @param pos2 第二个节点位置
 * @param minDistance 最小距离（默认考虑节点大小）
 * @returns 是否碰撞
 */
export function detectCollision(
  pos1: {x: number, y: number},
  pos2: {x: number, y: number},
  minDistance: number = 60
): boolean {
  const distance = Math.sqrt(
    Math.pow(pos1.x - pos2.x, 2) + Math.pow(pos1.y - pos2.y, 2)
  )
  return distance < minDistance
}

/**
 * 寻找避免碰撞的新位置（智能版本）
 * @param originalPos 原始位置
 * @param conflictPositions 冲突位置列表
 * @param draggedNodePos 被拖动节点的位置（用于优先选择靠近的方向）
 * @param minDistance 最小距离
 * @param maxAttempts 最大尝试次数
 * @returns 调整后的位置
 */
export function findNonCollidingPosition(
  originalPos: {x: number, y: number},
  conflictPositions: {x: number, y: number}[],
  draggedNodePos?: {x: number, y: number},
  minDistance: number = 60,
  maxAttempts: number = 12
): {x: number, y: number} {

  // 如果没有冲突，返回原位置
  if (conflictPositions.length === 0) {
    return originalPos
  }

  // 检查原位置是否有冲突
  const hasCollision = conflictPositions.some(pos =>
    detectCollision(originalPos, pos, minDistance)
  )

  if (!hasCollision) {
    return originalPos
  }

  // 生成候选位置，优先考虑靠近拖动节点的方向
  const candidates: Array<{pos: {x: number, y: number}, priority: number}> = []
  const adjustDistance = minDistance + 10

  // 生成多个候选位置
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    const angle = (attempt * 2 * Math.PI) / maxAttempts

    const candidatePos = {
      x: originalPos.x + Math.cos(angle) * adjustDistance,
      y: originalPos.y + Math.sin(angle) * adjustDistance
    }

    // 检查是否与其他节点碰撞
    const hasNewCollision = conflictPositions.some(pos =>
      detectCollision(candidatePos, pos, minDistance)
    )

    if (!hasNewCollision) {
      // 计算优先级：离拖动节点越近优先级越高
      let priority = 0
      if (draggedNodePos) {
        const distanceToDraggedNode = Math.sqrt(
          Math.pow(candidatePos.x - draggedNodePos.x, 2) +
          Math.pow(candidatePos.y - draggedNodePos.y, 2)
        )
        priority = 1000 - distanceToDraggedNode // 距离越近优先级越高
      }

      candidates.push({ pos: candidatePos, priority })
    }
  }

  // 如果有候选位置，选择优先级最高的
  if (candidates.length > 0) {
    candidates.sort((a, b) => b.priority - a.priority)
    return candidates[0].pos
  }

  // 如果没有找到合适位置，尝试更大的调整距离
  const largerDistance = adjustDistance * 1.5
  for (let attempt = 0; attempt < 6; attempt++) {
    const angle = (attempt * 2 * Math.PI) / 6

    const candidatePos = {
      x: originalPos.x + Math.cos(angle) * largerDistance,
      y: originalPos.y + Math.sin(angle) * largerDistance
    }

    const hasNewCollision = conflictPositions.some(pos =>
      detectCollision(candidatePos, pos, minDistance)
    )

    if (!hasNewCollision) {
      return candidatePos
    }
  }

  // 最后的后备方案：向拖动节点方向偏移
  if (draggedNodePos) {
    const directionX = draggedNodePos.x - originalPos.x
    const directionY = draggedNodePos.y - originalPos.y
    const length = Math.sqrt(directionX * directionX + directionY * directionY)

    if (length > 0) {
      const normalizedX = directionX / length
      const normalizedY = directionY / length

      return {
        x: originalPos.x + normalizedX * adjustDistance,
        y: originalPos.y + normalizedY * adjustDistance
      }
    }
  }

  // 最终后备方案
  return {
    x: originalPos.x + (Math.random() - 0.5) * adjustDistance,
    y: originalPos.y + (Math.random() - 0.5) * adjustDistance
  }
}

/**
 * 计算两点之间的距离
 */
export function calculateDistance(pos1: {x: number, y: number}, pos2: {x: number, y: number}): number {
  return Math.sqrt(Math.pow(pos1.x - pos2.x, 2) + Math.pow(pos1.y - pos2.y, 2))
}

/**
 * 检查是否需要重新布局叶子节点
 * @param draggedNodePos 被拖动节点位置
 * @param leafPositions 叶子节点位置列表
 * @param otherNodePositions 其他节点位置（用于碰撞检测）
 * @param idealEdgeLength 理想边长度
 * @param tolerance 容忍度
 * @param minDistance 最小安全距离
 */
export function shouldReLayoutLeafNodes(
  draggedNodePos: {x: number, y: number},
  leafPositions: {x: number, y: number}[],
  otherNodePositions: {x: number, y: number}[] = [],
  idealEdgeLength: number = 120,
  tolerance: number = 0.4,
  minDistance: number = 60
): boolean {
  if (leafPositions.length === 0) return false

  // 首先检查是否有碰撞风险
  let hasCollisionRisk = false
  for (const leafPos of leafPositions) {
    for (const otherPos of otherNodePositions) {
      if (detectCollision(leafPos, otherPos, minDistance)) {
        hasCollisionRisk = true
        break
      }
    }
    if (hasCollisionRisk) break
  }

  // 如果没有碰撞风险，不需要重布局
  if (!hasCollisionRisk) {
    return false
  }

  // 有碰撞风险时，检查边长度是否合理
  let hasEdgeLengthIssue = false
  for (const leafPos of leafPositions) {
    const distance = calculateDistance(draggedNodePos, leafPos)
    const minLength = idealEdgeLength * (1 - tolerance)
    const maxLength = idealEdgeLength * (1 + tolerance)

    // 如果边太长或太短，需要重新布局
    if (distance < minLength || distance > maxLength) {
      hasEdgeLengthIssue = true
      break
    }
  }

  // 只有同时满足：有碰撞风险 AND 边长度不合理，才重布局
  return hasCollisionRisk && hasEdgeLengthIssue
}

/**
 * 检查是否可以恢复边长度（当前边太短且周围有空间）
 * @param draggedNodePos 被拖动节点位置
 * @param leafPositions 叶子节点位置列表
 * @param otherNodePositions 其他节点位置
 * @param idealEdgeLength 理想边长度
 * @param minEdgeLength 最小边长度阈值
 * @param minDistance 最小安全距离
 */
export function canRestoreEdgeLength(
  draggedNodePos: {x: number, y: number},
  leafPositions: {x: number, y: number}[],
  otherNodePositions: {x: number, y: number}[] = [],
  idealEdgeLength: number = 120,
  minEdgeLength: number = 80,
  minDistance: number = 60
): boolean {
  if (leafPositions.length === 0) return false

  // 检查是否有边太短
  let hasShortEdges = false
  for (const leafPos of leafPositions) {
    const distance = calculateDistance(draggedNodePos, leafPos)
    if (distance < minEdgeLength) {
      hasShortEdges = true
      break
    }
  }

  if (!hasShortEdges) return false

  // 检查是否有足够空间恢复到理想长度
  for (const leafPos of leafPositions) {
    const currentDistance = calculateDistance(draggedNodePos, leafPos)

    if (currentDistance < minEdgeLength) {
      // 计算恢复到理想长度的位置
      const direction = {
        x: leafPos.x - draggedNodePos.x,
        y: leafPos.y - draggedNodePos.y
      }

      const currentLength = Math.sqrt(direction.x * direction.x + direction.y * direction.y)
      if (currentLength === 0) continue

      const normalizedDirection = {
        x: direction.x / currentLength,
        y: direction.y / currentLength
      }

      const idealPosition = {
        x: draggedNodePos.x + normalizedDirection.x * idealEdgeLength,
        y: draggedNodePos.y + normalizedDirection.y * idealEdgeLength
      }

      // 检查理想位置是否与其他节点碰撞
      const wouldCollide = otherNodePositions.some(otherPos =>
        detectCollision(idealPosition, otherPos, minDistance)
      )

      if (wouldCollide) {
        return false // 如果任何一个叶子节点无法恢复，就不恢复
      }
    }
  }

  return true // 所有短边都可以安全恢复
}

/**
 * 计算叶子节点的最佳位置（圆形分布）
 * @param centerPos 中心节点位置
 * @param leafCount 叶子节点数量
 * @param conflictPositions 需要避免的位置
 * @param idealDistance 理想距离
 * @param minDistance 最小安全距离
 */
export function calculateOptimalLeafPositions(
  centerPos: {x: number, y: number},
  leafCount: number,
  conflictPositions: {x: number, y: number}[],
  idealDistance: number = 120,
  minDistance: number = 60
): {x: number, y: number}[] {
  const positions: {x: number, y: number}[] = []

  if (leafCount === 0) return positions

  // 计算每个叶子节点的角度
  const angleStep = (2 * Math.PI) / leafCount

  for (let i = 0; i < leafCount; i++) {
    let bestPosition: {x: number, y: number} | null = null

    // 尝试不同的距离，从理想距离开始
    for (let distanceMultiplier = 1; distanceMultiplier <= 2; distanceMultiplier += 0.2) {
      const currentDistance = idealDistance * distanceMultiplier

      // 尝试不同的角度偏移
      for (let angleOffset = 0; angleOffset < 2 * Math.PI; angleOffset += Math.PI / 8) {
        const angle = i * angleStep + angleOffset

        const candidatePos = {
          x: centerPos.x + Math.cos(angle) * currentDistance,
          y: centerPos.y + Math.sin(angle) * currentDistance
        }

        // 检查是否与所有冲突位置（包括已放置的叶子节点）都没有碰撞
        const allConflictPos = [...conflictPositions, ...positions]
        const hasCollision = allConflictPos.some(pos =>
          detectCollision(candidatePos, pos, minDistance)
        )

        if (!hasCollision) {
          bestPosition = candidatePos
          break
        }
      }

      if (bestPosition) break
    }

    // 如果找到了合适的位置，添加到结果中
    if (bestPosition) {
      positions.push(bestPosition)
    } else {
      // 如果没找到，使用默认位置（可能会有轻微重叠）
      const angle = i * angleStep
      positions.push({
        x: centerPos.x + Math.cos(angle) * idealDistance,
        y: centerPos.y + Math.sin(angle) * idealDistance
      })
    }
  }

  return positions
}

/**
 * 恢复叶子节点到理想边长度（带碰撞检测和重叠避免）
 * @param draggedNodePos 被拖动节点位置
 * @param leafPositions 当前叶子节点位置
 * @param otherNodePositions 其他节点位置
 * @param idealEdgeLength 理想边长度
 * @param minEdgeLength 最小边长度阈值
 * @param minDistance 最小安全距离
 */
export function restoreLeafNodePositions(
  draggedNodePos: {x: number, y: number},
  leafPositions: {x: number, y: number}[],
  otherNodePositions: {x: number, y: number}[] = [],
  idealEdgeLength: number = 120,
  minEdgeLength: number = 80,
  minDistance: number = 60
): {x: number, y: number}[] {
  const restoredPositions: {x: number, y: number}[] = []

  for (let i = 0; i < leafPositions.length; i++) {
    const leafPos = leafPositions[i]
    const currentDistance = calculateDistance(draggedNodePos, leafPos)

    if (currentDistance < minEdgeLength) {
      // 需要恢复的短边
      const direction = {
        x: leafPos.x - draggedNodePos.x,
        y: leafPos.y - draggedNodePos.y
      }

      const currentLength = Math.sqrt(direction.x * direction.x + direction.y * direction.y)
      let idealPosition: {x: number, y: number}

      if (currentLength === 0) {
        // 如果重叠，使用角度分布
        const angle = (i * 2 * Math.PI) / leafPositions.length
        idealPosition = {
          x: draggedNodePos.x + Math.cos(angle) * idealEdgeLength,
          y: draggedNodePos.y + Math.sin(angle) * idealEdgeLength
        }
      } else {
        const normalizedDirection = {
          x: direction.x / currentLength,
          y: direction.y / currentLength
        }

        idealPosition = {
          x: draggedNodePos.x + normalizedDirection.x * idealEdgeLength,
          y: draggedNodePos.y + normalizedDirection.y * idealEdgeLength
        }
      }

      // 检查理想位置是否与其他固定节点和已恢复的叶子节点碰撞
      const allConflictPositions = [...otherNodePositions, draggedNodePos, ...restoredPositions]
      const wouldCollide = allConflictPositions.some(otherPos =>
        detectCollision(idealPosition, otherPos, minDistance)
      )

      if (!wouldCollide) {
        restoredPositions.push(idealPosition)
      } else {
        // 如果理想位置有碰撞，寻找无碰撞的位置
        const adjustedPosition = findNonCollidingPosition(
          idealPosition,
          allConflictPositions,
          draggedNodePos,
          minDistance,
          12 // 最大尝试次数
        )
        restoredPositions.push(adjustedPosition)
      }
    } else {
      // 边长度正常，但仍需检查是否与已恢复的叶子节点重叠
      const allConflictPositions = [...otherNodePositions, draggedNodePos, ...restoredPositions]
      const hasCollision = allConflictPositions.some(otherPos =>
        detectCollision(leafPos, otherPos, minDistance)
      )

      if (!hasCollision) {
        restoredPositions.push(leafPos)
      } else {
        // 如果当前位置有碰撞，调整位置
        const adjustedPosition = findNonCollidingPosition(
          leafPos,
          allConflictPositions,
          draggedNodePos,
          minDistance,
          12 // 最大尝试次数
        )
        restoredPositions.push(adjustedPosition)
      }
    }
  }

  return restoredPositions
}