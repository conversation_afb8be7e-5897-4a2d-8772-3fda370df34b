import React, { useRef, useEffect, useCallback, useState } from 'react'
import { GraphData, GraphNode, GraphEdge } from './types'

interface CustomMinimapProps {
  cyInstance: any
  data: GraphData
  nodeTypeColors: Record<string, string>
  className?: string
}

interface ViewportInfo {
  x: number
  y: number
  width: number
  height: number
  zoom: number
}

export function CustomMinimap({
  cyInstance,
  data,
  nodeTypeColors,
  className = ''
}: CustomMinimapProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragMode, setDragMode] = useState<'none' | 'viewport' | 'jump'>('none')
  const isDraggingRef = useRef(false)
  const dragViewportRef = useRef<ViewportInfo | null>(null)
  const panTimeoutRef = useRef<number | null>(null)
  const [viewport, setViewport] = useState<ViewportInfo>({
    x: 0, y: 0, width: 0, height: 0, zoom: 1
  })

  // 添加一个 ref 来跟踪最新的视口数据
  const viewportRef = useRef<ViewportInfo>({
    x: 0, y: 0, width: 0, height: 0, zoom: 1
  })
  const renderTimeoutRef = useRef<number | null>(null)

  // Minimap 尺寸配置
  const MINIMAP_WIDTH = 200
  const MINIMAP_HEIGHT = 150
  const PADDING = 10

  // 计算图谱的边界框
  const calculateGraphBounds = useCallback(() => {
    if (!data.nodes.length) {
      return { minX: 0, minY: 0, maxX: 100, maxY: 100 }
    }

    let minX = Infinity, minY = Infinity
    let maxX = -Infinity, maxY = -Infinity

    // 优先使用 cytoscape 实例的实时位置
    if (cyInstance && !cyInstance.destroyed()) {
      try {
        const nodes = cyInstance.nodes()
        if (nodes.length > 0) {
          nodes.forEach((node: any) => {
            if (node.length > 0) {
              const pos = node.position()
              if (pos && typeof pos.x === 'number' && typeof pos.y === 'number') {
                minX = Math.min(minX, pos.x)
                minY = Math.min(minY, pos.y)
                maxX = Math.max(maxX, pos.x)
                maxY = Math.max(maxY, pos.y)
              }
            }
          })
        }
      } catch (error) {
        // 静默处理获取节点位置的错误
      }
    }

    // 如果没有从 cytoscape 获取到有效位置，使用数据中的位置信息
    if (minX === Infinity) {
      data.nodes.forEach(node => {
        const x = (node as any).position?.x || 0
        const y = (node as any).position?.y || 0
        minX = Math.min(minX, x)
        minY = Math.min(minY, y)
        maxX = Math.max(maxX, x)
        maxY = Math.max(maxY, y)
      })
    }

    // 确保有有效的边界
    if (minX === Infinity) {
      return { minX: 0, minY: 0, maxX: 100, maxY: 100 }
    }

    // 添加一些边距
    const margin = 50
    return {
      minX: minX - margin,
      minY: minY - margin,
      maxX: maxX + margin,
      maxY: maxY + margin
    }
  }, []) // 移除依赖，在函数内部直接使用最新值

  // 更新视口信息
  const updateViewport = useCallback(() => {
    if (!cyInstance || cyInstance.destroyed()) return

    const extent = cyInstance.extent()
    const zoom = cyInstance.zoom()

    setViewport({
      x: extent.x1,
      y: extent.y1,
      width: extent.x2 - extent.x1,
      height: extent.y2 - extent.y1,
      zoom
    })
  }, [cyInstance])

  // 渲染 minimap
  const renderMinimap = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    try {
      // 清空画布
      ctx.clearRect(0, 0, MINIMAP_WIDTH, MINIMAP_HEIGHT)

    // 计算图谱边界
    const bounds = calculateGraphBounds()
    const graphWidth = bounds.maxX - bounds.minX
    const graphHeight = bounds.maxY - bounds.minY



    // 计算缩放比例，保持宽高比
    const scaleX = (MINIMAP_WIDTH - 2 * PADDING) / graphWidth
    const scaleY = (MINIMAP_HEIGHT - 2 * PADDING) / graphHeight
    const scale = Math.min(scaleX, scaleY)

    // 计算偏移量，使图谱居中
    const offsetX = PADDING + (MINIMAP_WIDTH - 2 * PADDING - graphWidth * scale) / 2
    const offsetY = PADDING + (MINIMAP_HEIGHT - 2 * PADDING - graphHeight * scale) / 2

    // 坐标转换函数
    const transformX = (x: number) => (x - bounds.minX) * scale + offsetX
    const transformY = (y: number) => (y - bounds.minY) * scale + offsetY

    // 绘制边
    ctx.strokeStyle = '#B0BEC5'
    ctx.lineWidth = 1
    ctx.globalAlpha = 0.6

    if (cyInstance && !cyInstance.destroyed()) {
      try {
        cyInstance.edges().forEach((edge: any) => {
          const sourceNode = edge.source()
          const targetNode = edge.target()

          if (sourceNode.length > 0 && targetNode.length > 0) {
            const sourcePos = sourceNode.position()
            const targetPos = targetNode.position()

            ctx.beginPath()
            ctx.moveTo(transformX(sourcePos.x), transformY(sourcePos.y))
            ctx.lineTo(transformX(targetPos.x), transformY(targetPos.y))
            ctx.stroke()
          }
        })
      } catch (error) {
        // 静默处理绘制边的错误
      }
    }

    // 绘制节点
    ctx.globalAlpha = 0.8
    if (cyInstance && !cyInstance.destroyed()) {
      try {
        cyInstance.nodes().forEach((node: any) => {
          if (node.length > 0) {
            const pos = node.position()
            const nodeData = node.data()

            // 获取节点颜色
            const labels = nodeData.labels || []
            const type = nodeData.type
            let color = '#8993A4' // 默认颜色

            if (Array.isArray(labels) && labels.length > 0) {
              for (const label of labels) {
                if (nodeTypeColors[label]) {
                  color = nodeTypeColors[label]
                  break
                }
              }
            } else if (nodeTypeColors[type]) {
              color = nodeTypeColors[type]
            }

            const x = transformX(pos.x)
            const y = transformY(pos.y)
            const radius = Math.max(2, 4 * scale)

            ctx.fillStyle = color
            ctx.beginPath()
            ctx.arc(x, y, radius, 0, 2 * Math.PI)
            ctx.fill()
          }
        })
      } catch (error) {
        // 静默处理绘制节点的错误
      }
    }

    // 绘制视口指示器 - 拖动时使用临时数据，否则使用正常数据
    const currentViewport = isDraggingRef.current && dragViewportRef.current
      ? dragViewportRef.current
      : viewportRef.current

    if (currentViewport.width > 0 && currentViewport.height > 0) {
      const viewX = transformX(currentViewport.x)
      const viewY = transformY(currentViewport.y)
      const viewWidth = currentViewport.width * scale
      const viewHeight = currentViewport.height * scale



      // 确保视口指示器在画布范围内
      if (viewX < MINIMAP_WIDTH && viewY < MINIMAP_HEIGHT &&
          viewX + viewWidth > 0 && viewY + viewHeight > 0) {

        // 半透明填充
        ctx.globalAlpha = 0.3
        ctx.fillStyle = '#3b82f6'
        ctx.fillRect(viewX, viewY, viewWidth, viewHeight)

        // 边框
        ctx.globalAlpha = 1
        ctx.strokeStyle = '#1d4ed8'
        ctx.lineWidth = 2
        ctx.strokeRect(viewX, viewY, viewWidth, viewHeight)

        // 内部边框（增强可见性）
        ctx.strokeStyle = '#ffffff'
        ctx.lineWidth = 1
        ctx.strokeRect(viewX + 1, viewY + 1, viewWidth - 2, viewHeight - 2)
      }
    }
    } catch (error) {
      // 静默处理渲染错误
    }
  }, [nodeTypeColors, viewport]) // 移除会导致循环的依赖

  // 节流渲染函数 - 直接渲染，避免依赖循环
  const throttledRender = useCallback(() => {
    if (renderTimeoutRef.current) {
      clearTimeout(renderTimeoutRef.current)
    }
    renderTimeoutRef.current = setTimeout(() => {
      renderMinimap()
    }, 16) // 约60fps
  }, []) // 空依赖数组，避免循环

  // 立即渲染函数 - 用于拖动时的实时更新
  const immediateRender = useCallback(() => {
    renderMinimap()
  }, [])

  // 检测点击是否在视口指示器内
  const isClickInViewport = useCallback((clickX: number, clickY: number) => {
    const currentViewport = viewportRef.current
    if (currentViewport.width <= 0 || currentViewport.height <= 0) return false

    const bounds = calculateGraphBounds()
    const graphWidth = bounds.maxX - bounds.minX
    const graphHeight = bounds.maxY - bounds.minY
    const scaleX = (MINIMAP_WIDTH - 2 * PADDING) / graphWidth
    const scaleY = (MINIMAP_HEIGHT - 2 * PADDING) / graphHeight
    const scale = Math.min(scaleX, scaleY)
    const offsetX = PADDING + (MINIMAP_WIDTH - 2 * PADDING - graphWidth * scale) / 2
    const offsetY = PADDING + (MINIMAP_HEIGHT - 2 * PADDING - graphHeight * scale) / 2

    const transformX = (x: number) => (x - bounds.minX) * scale + offsetX
    const transformY = (y: number) => (y - bounds.minY) * scale + offsetY

    const viewX = transformX(currentViewport.x)
    const viewY = transformY(currentViewport.y)
    const viewWidth = currentViewport.width * scale
    const viewHeight = currentViewport.height * scale

    const isInside = clickX >= viewX && clickX <= viewX + viewWidth &&
                     clickY >= viewY && clickY <= viewY + viewHeight

    return isInside
  }, [])



  // 处理鼠标事件
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    if (!cyInstance || cyInstance.destroyed()) return

    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return

    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    // 检测点击位置
    if (isClickInViewport(x, y)) {
      // 点击在视口指示器内 - 进入拖动模式
      setIsDragging(true)
      setDragMode('viewport')
      isDraggingRef.current = true
    } else {
      // 点击在空白区域 - 立即跳转
      setDragMode('jump')

      // 计算点击位置对应的图谱坐标并立即移动
      const bounds = calculateGraphBounds()
      const graphWidth = bounds.maxX - bounds.minX
      const graphHeight = bounds.maxY - bounds.minY
      const scaleX = (MINIMAP_WIDTH - 2 * PADDING) / graphWidth
      const scaleY = (MINIMAP_HEIGHT - 2 * PADDING) / graphHeight
      const scale = Math.min(scaleX, scaleY)
      const offsetX = PADDING + (MINIMAP_WIDTH - 2 * PADDING - graphWidth * scale) / 2
      const offsetY = PADDING + (MINIMAP_HEIGHT - 2 * PADDING - graphHeight * scale) / 2

      const graphX = (x - offsetX) / scale + bounds.minX
      const graphY = (y - offsetY) / scale + bounds.minY

      cyInstance.center({ x: graphX, y: graphY })
    }
  }, [cyInstance, isClickInViewport])

  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    // 只处理视口拖动模式
    if (!isDragging || dragMode !== 'viewport' || !cyInstance || cyInstance.destroyed()) return

    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return

    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    // 计算移动位置对应的图谱坐标
    const bounds = calculateGraphBounds()
    const graphWidth = bounds.maxX - bounds.minX
    const graphHeight = bounds.maxY - bounds.minY
    const scaleX = (MINIMAP_WIDTH - 2 * PADDING) / graphWidth
    const scaleY = (MINIMAP_HEIGHT - 2 * PADDING) / graphHeight
    const scale = Math.min(scaleX, scaleY)
    const offsetX = PADDING + (MINIMAP_WIDTH - 2 * PADDING - graphWidth * scale) / 2
    const offsetY = PADDING + (MINIMAP_HEIGHT - 2 * PADDING - graphHeight * scale) / 2

    const graphX = (x - offsetX) / scale + bounds.minX
    const graphY = (y - offsetY) / scale + bounds.minY

    // 更新临时视口数据并节流移动画布
    const containerWidth = cyInstance.width()
    const containerHeight = cyInstance.height()
    const zoom = cyInstance.zoom()

    if (containerWidth > 0 && containerHeight > 0) {
      // 计算新的视口位置
      const newViewport = {
        x: graphX - containerWidth / (2 * zoom),
        y: graphY - containerHeight / (2 * zoom),
        width: containerWidth / zoom,
        height: containerHeight / zoom,
        zoom
      }

      dragViewportRef.current = newViewport

      // 立即重新渲染 minimap
      immediateRender()

      // 节流移动画布（每16ms最多移动一次，约60fps）
      if (panTimeoutRef.current) {
        clearTimeout(panTimeoutRef.current)
      }
      panTimeoutRef.current = setTimeout(() => {
        if (cyInstance && !cyInstance.destroyed()) {
          const targetPan = {
            x: containerWidth / 2 - graphX * zoom,
            y: containerHeight / 2 - graphY * zoom
          }
          cyInstance.pan(targetPan)
        }
      }, 16) // 16ms 节流，约60fps，提供更流畅的跟随体验
    }
  }, [isDragging, dragMode, cyInstance])

  const handleMouseUp = useCallback(() => {
    // 清理节流定时器
    if (panTimeoutRef.current) {
      clearTimeout(panTimeoutRef.current)
      panTimeoutRef.current = null
    }

    // 如果是拖动模式，确保最终位置正确
    if (dragMode === 'viewport' && dragViewportRef.current && cyInstance && !cyInstance.destroyed()) {
      const targetViewport = dragViewportRef.current
      const containerWidth = cyInstance.width()
      const containerHeight = cyInstance.height()

      if (containerWidth > 0 && containerHeight > 0) {
        // 计算目标中心点
        const targetCenterX = targetViewport.x + targetViewport.width / 2
        const targetCenterY = targetViewport.y + targetViewport.height / 2

        // 最终移动到准确位置
        const targetPan = {
          x: containerWidth / 2 - targetCenterX * targetViewport.zoom,
          y: containerHeight / 2 - targetCenterY * targetViewport.zoom
        }

        cyInstance.pan(targetPan)

        // 更新最终视口
        viewportRef.current = targetViewport
        setViewport(targetViewport)
      }
    }

    setIsDragging(false)
    setDragMode('none')
    isDraggingRef.current = false
    dragViewportRef.current = null
  }, [dragMode, cyInstance])

  // 添加全局鼠标事件监听，确保拖拽在整个窗口范围内都有效
  useEffect(() => {
    if (!isDragging || dragMode !== 'viewport') return

    const handleGlobalMouseMove = (event: MouseEvent) => {
      if (!cyInstance || cyInstance.destroyed() || !canvasRef.current) return

      const rect = canvasRef.current.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top

      // 计算移动位置对应的图谱坐标
      const bounds = calculateGraphBounds()
      const graphWidth = bounds.maxX - bounds.minX
      const graphHeight = bounds.maxY - bounds.minY
      const scaleX = (MINIMAP_WIDTH - 2 * PADDING) / graphWidth
      const scaleY = (MINIMAP_HEIGHT - 2 * PADDING) / graphHeight
      const scale = Math.min(scaleX, scaleY)
      const offsetX = PADDING + (MINIMAP_WIDTH - 2 * PADDING - graphWidth * scale) / 2
      const offsetY = PADDING + (MINIMAP_HEIGHT - 2 * PADDING - graphHeight * scale) / 2

      const graphX = (x - offsetX) / scale + bounds.minX
      const graphY = (y - offsetY) / scale + bounds.minY

      // 更新临时视口数据并节流移动画布
      const containerWidth = cyInstance.width()
      const containerHeight = cyInstance.height()
      const zoom = cyInstance.zoom()

      if (containerWidth > 0 && containerHeight > 0) {
        // 计算新的视口位置
        const newViewport = {
          x: graphX - containerWidth / (2 * zoom),
          y: graphY - containerHeight / (2 * zoom),
          width: containerWidth / zoom,
          height: containerHeight / zoom,
          zoom
        }

        dragViewportRef.current = newViewport

        // 立即重新渲染 minimap
        immediateRender()

        // 节流移动画布（约60fps）
        if (panTimeoutRef.current) {
          clearTimeout(panTimeoutRef.current)
        }
        panTimeoutRef.current = setTimeout(() => {
          if (cyInstance && !cyInstance.destroyed()) {
            const targetPan = {
              x: containerWidth / 2 - graphX * zoom,
              y: containerHeight / 2 - graphY * zoom
            }
            cyInstance.pan(targetPan)
          }
        }, 16)
      }
    }

    const handleGlobalMouseUp = () => {
      // 如果是拖动模式，在结束时才真正移动 cytoscape 视图
      if (dragMode === 'viewport' && dragViewportRef.current && cyInstance && !cyInstance.destroyed()) {
        const targetViewport = dragViewportRef.current
        const containerWidth = cyInstance.width()
        const containerHeight = cyInstance.height()

        if (containerWidth > 0 && containerHeight > 0) {
          // 计算目标中心点
          const targetCenterX = targetViewport.x + targetViewport.width / 2
          const targetCenterY = targetViewport.y + targetViewport.height / 2

          // 使用 pan 方法移动到目标位置
          const targetPan = {
            x: containerWidth / 2 - targetCenterX * targetViewport.zoom,
            y: containerHeight / 2 - targetCenterY * targetViewport.zoom
          }

          cyInstance.pan(targetPan)

          // 更新最终视口
          viewportRef.current = targetViewport
          setViewport(targetViewport)
        }
      }

      setIsDragging(false)
      setDragMode('none')
      isDraggingRef.current = false
      dragViewportRef.current = null
    }

    document.addEventListener('mousemove', handleGlobalMouseMove)
    document.addEventListener('mouseup', handleGlobalMouseUp)

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
    }
  }, [isDragging, dragMode, cyInstance])

  // 监听 cytoscape 事件
  useEffect(() => {
    if (!cyInstance || cyInstance.destroyed()) return

    let viewportUpdateTimeout: number | null = null
    let renderUpdateTimeout: number | null = null

    const handleViewportChange = () => {
      // 如果正在拖动，忽略 cytoscape 的视口更新事件，避免冲突
      if (isDraggingRef.current) {
        return
      }

      if (viewportUpdateTimeout) {
        clearTimeout(viewportUpdateTimeout)
      }
      viewportUpdateTimeout = setTimeout(() => {
        if (!cyInstance || cyInstance.destroyed()) return
        try {
          const extent = cyInstance.extent()
          const zoom = cyInstance.zoom()

          // 检查 extent 是否有效
          if (extent && typeof extent.x1 === 'number' && typeof extent.x2 === 'number' &&
              typeof extent.y1 === 'number' && typeof extent.y2 === 'number') {
            const newViewport = {
              x: extent.x1,
              y: extent.y1,
              width: extent.x2 - extent.x1,
              height: extent.y2 - extent.y1,
              zoom
            }
            viewportRef.current = newViewport
            setViewport(newViewport)
          } else {
            // 使用备用方法获取视口信息
            const pan = cyInstance.pan()
            const containerWidth = cyInstance.width()
            const containerHeight = cyInstance.height()

            if (containerWidth > 0 && containerHeight > 0) {
              const newViewport = {
                x: -pan.x / zoom,
                y: -pan.y / zoom,
                width: containerWidth / zoom,
                height: containerHeight / zoom,
                zoom
              }
              viewportRef.current = newViewport
              setViewport(newViewport)
            }
          }
        } catch (error) {
          // 静默处理获取视口信息的错误
        }
      }, 16) // 节流更新
    }

    const handleRenderUpdate = (event?: any) => {
      if (renderUpdateTimeout) {
        clearTimeout(renderUpdateTimeout)
      }
      renderUpdateTimeout = setTimeout(() => {
        // 直接调用渲染，避免依赖循环
        if (renderTimeoutRef.current) {
          clearTimeout(renderTimeoutRef.current)
        }
        renderTimeoutRef.current = setTimeout(() => {
          renderMinimap()
        }, 16)
      }, 32) // 节点位置变化后重新渲染，约30fps
    }

    // 延迟添加事件监听器，确保 cytoscape 完全初始化
    const setupEventListeners = () => {
      try {
        // 监听视口变化事件 - 简化事件监听
        cyInstance.on('pan', handleViewportChange)
        cyInstance.on('zoom', handleViewportChange)
        cyInstance.on('resize', handleViewportChange)

        // 添加更多可能触发视口变化的事件
        cyInstance.on('fit', handleViewportChange)
        cyInstance.on('center', handleViewportChange)

        // 监听节点位置变化和数据更新事件
        cyInstance.on('position', 'node', handleRenderUpdate) // 节点拖动
        cyInstance.on('free', 'node', handleRenderUpdate) // 节点拖动结束
        cyInstance.on('add remove', handleRenderUpdate) // 节点/边添加删除
        cyInstance.on('data', handleRenderUpdate) // 数据变化
        cyInstance.on('style', handleRenderUpdate) // 样式变化
        cyInstance.on('layoutstop', handleRenderUpdate) // 布局完成

        // 初始更新 - 延迟执行确保 cytoscape 完全初始化
        setTimeout(() => {
          handleViewportChange()
          handleRenderUpdate({ type: 'init' })
        }, 100)
      } catch (error) {
        // 静默处理设置事件监听器的错误
      }
    }

    // 立即尝试设置，如果失败则延迟重试
    if (cyInstance.nodes && cyInstance.nodes().length > 0) {
      setupEventListeners()
    } else {
      setTimeout(setupEventListeners, 100)
    }

    return () => {
      if (viewportUpdateTimeout) {
        clearTimeout(viewportUpdateTimeout)
      }
      if (renderUpdateTimeout) {
        clearTimeout(renderUpdateTimeout)
      }
      if (!cyInstance.destroyed()) {
        try {
          cyInstance.off('pan', handleViewportChange)
          cyInstance.off('zoom', handleViewportChange)
          cyInstance.off('resize', handleViewportChange)
          cyInstance.off('fit', handleViewportChange)
          cyInstance.off('center', handleViewportChange)
          cyInstance.off('position', 'node', handleRenderUpdate)
          cyInstance.off('free', 'node', handleRenderUpdate)
          cyInstance.off('add remove', handleRenderUpdate)
          cyInstance.off('data', handleRenderUpdate)
          cyInstance.off('style', handleRenderUpdate)
          cyInstance.off('layoutstop', handleRenderUpdate)
        } catch (error) {
          // 静默处理清理事件监听器的错误
        }
      }
    }
  }, [cyInstance]) // 移除会导致循环的依赖

  // 监听数据变化，重新渲染
  useEffect(() => {
    throttledRender()
  }, [data.nodes.length, data.edges.length]) // 只监听数据长度变化

  // 监听视口变化，重新渲染
  useEffect(() => {
    throttledRender()
  }, [viewport.x, viewport.y, viewport.width, viewport.height, viewport.zoom])

  // 强制初始化视口 - 使用定时器确保 cytoscape 完全加载
  useEffect(() => {
    if (!cyInstance || cyInstance.destroyed()) return

    const forceUpdateViewport = () => {
      try {
        const pan = cyInstance.pan()
        const zoom = cyInstance.zoom()
        const containerWidth = cyInstance.width()
        const containerHeight = cyInstance.height()

        if (containerWidth > 0 && containerHeight > 0 && zoom > 0) {
          const newViewport = {
            x: -pan.x / zoom,
            y: -pan.y / zoom,
            width: containerWidth / zoom,
            height: containerHeight / zoom,
            zoom
          }
          viewportRef.current = newViewport
          setViewport(newViewport)
        }
      } catch (error) {
        // 静默处理强制更新视口的错误
      }
    }

    // 多次尝试，确保能获取到有效数据
    const timers = [
      setTimeout(forceUpdateViewport, 100),
      setTimeout(forceUpdateViewport, 500),
      setTimeout(forceUpdateViewport, 1000)
    ]

    return () => {
      timers.forEach(timer => clearTimeout(timer))
    }
  }, [cyInstance])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (renderTimeoutRef.current) {
        clearTimeout(renderTimeoutRef.current)
      }
    }
  }, [])

  return (
    <div
      ref={containerRef}
      className={`fixed bottom-5 left-5 bg-white/95 backdrop-blur-sm border border-gray-300 rounded-xl shadow-lg overflow-hidden z-50 ${className}`}
      style={{
        width: MINIMAP_WIDTH,
        height: MINIMAP_HEIGHT
      }}
    >
      <canvas
        ref={canvasRef}
        width={MINIMAP_WIDTH}
        height={MINIMAP_HEIGHT}
        className={isDragging ? "cursor-grabbing" : "cursor-grab"}
        onMouseDown={(e) => {
          e.preventDefault()
          e.stopPropagation()
          handleMouseDown(e)
        }}
        onMouseMove={(e) => {
          e.preventDefault()
          e.stopPropagation()
          handleMouseMove(e)
        }}
        onMouseUp={(e) => {
          e.preventDefault()
          e.stopPropagation()
          handleMouseUp()
        }}
        onMouseLeave={(e) => {
          e.preventDefault()
          e.stopPropagation()
          handleMouseUp()
        }}
        style={{ userSelect: 'none' }}
      />
    </div>
  )
}
