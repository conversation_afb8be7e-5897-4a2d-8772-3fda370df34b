import { useEffect, useRef, useState } from 'react'
import Editor from '@monaco-editor/react'
import * as monaco from 'monaco-editor'
import { neo4jApi } from '@/services/api'

interface CypherEditorProps {
  value: string
  onChange: (value: string) => void
  onExecute?: () => void
  height?: number | string
  readonly?: boolean
}

interface SchemaInfo {
  node_labels: string[]
  relationship_types: string[]
  property_keys: string[]
  functions?: string[]
}

export function CypherEditor({
  value,
  onChange,
  onExecute,
  height = 120,
  readonly = false
}: CypherEditorProps) {
  const editorRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null)
  const [schema, setSchema] = useState<SchemaInfo | null>(null)

  // 加载 schema 信息
  useEffect(() => {
    const loadSchema = async () => {
      try {
        const response = await neo4jApi.getSchema()
        console.log('Schema 数据:', response)
        setSchema(response)
      } catch (error) {
        console.error('获取 schema 失败:', error)
      }
    }

    loadSchema()
  }, [])

  // 配置 Monaco Editor
  const handleEditorMount = (editor: monaco.editor.IStandaloneCodeEditor, monaco: typeof import('monaco-editor')) => {
    editorRef.current = editor

    // 注册 Cypher 语言
    if (!monaco.languages.getLanguages().some(lang => lang.id === 'cypher')) {
      monaco.languages.register({ id: 'cypher' })

      // 定义 Cypher 语法高亮
      monaco.languages.setMonarchTokensProvider('cypher', {
        defaultToken: 'invalid',
        keywords: [
          'MATCH', 'RETURN', 'WHERE', 'CREATE', 'DELETE', 'SET', 'REMOVE', 'MERGE',
          'WITH', 'UNWIND', 'FOREACH', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END',
          'AND', 'OR', 'NOT', 'XOR', 'IN', 'IS', 'NULL', 'TRUE', 'FALSE',
          'DISTINCT', 'ORDER', 'BY', 'ASC', 'DESC', 'SKIP', 'LIMIT',
          'OPTIONAL', 'UNION', 'ALL', 'CALL', 'YIELD', 'LOAD', 'CSV', 'FROM',
          'HEADERS', 'AS', 'FIELDTERMINATOR', 'USE', 'CONSTRAINT', 'INDEX',
          'DROP', 'EXPLAIN', 'PROFILE', 'START', 'DETACH'
        ],
        functions: [
          'count', 'sum', 'avg', 'min', 'max', 'collect', 'size', 'length',
          'head', 'tail', 'last', 'extract', 'filter', 'reduce', 'any', 'all',
          'none', 'single', 'exists', 'id', 'type', 'labels', 'keys', 'properties',
          'startNode', 'endNode', 'nodes', 'relationships', 'reverse', 'range',
          'abs', 'ceil', 'floor', 'round', 'sign', 'sqrt', 'rand', 'toInteger',
          'toFloat', 'toString', 'split', 'substring', 'left', 'right', 'trim',
          'replace', 'upper', 'lower'
        ],
        operators: [
          '=', '<>', '<', '>', '<=', '>=', '+', '-', '*', '/', '%', '^',
          '=~', 'STARTS', 'ENDS', 'CONTAINS'
        ],
        tokenizer: {
          root: [
            [/[a-zA-Z_]\w*/, {
              cases: {
                '@keywords': 'keyword',
                '@functions': 'predefined',
                '@default': 'identifier'
              }
            }],
            [/"([^"\\]|\\.)*$/, 'string.invalid'],
            [/"/, 'string', '@string'],
            [/'([^'\\]|\\.)*$/, 'string.invalid'],
            [/'/, 'string', '@string_single'],
            [/`([^`\\]|\\.)*$/, 'string.invalid'],
            [/`/, 'identifier.quote', '@identifier_quoted'],
            [/\d+\.\d+/, 'number.float'],
            [/\d+/, 'number'],
            [/[{}()\[\]]/, '@brackets'],
            [/[<>]=?/, 'operator'],
            [/[+\-*/%^]/, 'operator'],
            [/[=<>!]=?/, 'operator'],
            [/[&|~]/, 'operator'],
            [/[;,.]/, 'delimiter'],
            [/:/, 'delimiter.colon'],
            [/->|<-/, 'operator.arrow'],
            [/\/\/.*$/, 'comment'],
            [/\/\*/, 'comment', '@comment'],
            [/\s+/, 'white']
          ],
          string: [
            [/[^\\"]+/, 'string'],
            [/\\./, 'string.escape'],
            [/"/, 'string', '@pop']
          ],
          string_single: [
            [/[^\\']+/, 'string'],
            [/\\./, 'string.escape'],
            [/'/, 'string', '@pop']
          ],
          identifier_quoted: [
            [/[^`\\]+/, 'identifier'],
            [/\\./, 'identifier.escape'],
            [/`/, 'identifier.quote', '@pop']
          ],
          comment: [
            [/[^/*]+/, 'comment'],
            [/\*\//, 'comment', '@pop'],
            [/[/*]/, 'comment']
          ]
        }
      })

      // 配置语言特性
      monaco.languages.setLanguageConfiguration('cypher', {
        comments: {
          lineComment: '//',
          blockComment: ['/*', '*/']
        },
        brackets: [
          ['{', '}'],
          ['[', ']'],
          ['(', ')']
        ],
        autoClosingPairs: [
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '(', close: ')' },
          { open: '"', close: '"' },
          { open: "'", close: "'" },
          { open: '`', close: '`' }
        ],
        surroundingPairs: [
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '(', close: ')' },
          { open: '"', close: '"' },
          { open: "'", close: "'" },
          { open: '`', close: '`' }
        ]
      })

      // 注册自动完成提供程序
      monaco.languages.registerCompletionItemProvider('cypher', {
        provideCompletionItems: (model, position) => {
          const word = model.getWordUntilPosition(position)
          const range = {
            startLineNumber: position.lineNumber,
            endLineNumber: position.lineNumber,
            startColumn: word.startColumn,
            endColumn: word.endColumn
          }

          const suggestions: monaco.languages.CompletionItem[] = []
          
          // Cypher 关键字
          const keywords = [
            'MATCH', 'RETURN', 'WHERE', 'CREATE', 'DELETE', 'SET', 'REMOVE', 'MERGE',
            'WITH', 'UNWIND', 'FOREACH', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END',
            'AND', 'OR', 'NOT', 'XOR', 'IN', 'IS', 'NULL', 'TRUE', 'FALSE',
            'DISTINCT', 'ORDER', 'BY', 'ASC', 'DESC', 'SKIP', 'LIMIT',
            'OPTIONAL', 'UNION', 'ALL', 'CALL', 'YIELD', 'DETACH'
          ]

          keywords.forEach(keyword => {
            suggestions.push({
              label: keyword,
              kind: monaco.languages.CompletionItemKind.Keyword,
              insertText: keyword,
              range: range,
              detail: 'Cypher 关键字'
            })
          })

          // 函数
          const functions = [
            'count', 'sum', 'avg', 'min', 'max', 'collect', 'size', 'length',
            'head', 'tail', 'last', 'extract', 'filter', 'reduce', 'any', 'all',
            'none', 'single', 'exists', 'id', 'type', 'labels', 'keys', 'properties',
            'startNode', 'endNode', 'nodes', 'relationships', 'reverse', 'range',
            'abs', 'ceil', 'floor', 'round', 'sign', 'sqrt', 'rand', 'toInteger',
            'toFloat', 'toString', 'split', 'substring', 'left', 'right', 'trim',
            'replace', 'upper', 'lower'
          ]

          functions.forEach(func => {
            suggestions.push({
              label: func,
              kind: monaco.languages.CompletionItemKind.Function,
              insertText: `${func}()`,
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              range: range,
              detail: 'Cypher 函数'
            })
          })

          // 如果有 schema 数据，添加节点标签和关系类型
          if (schema) {
            // 节点标签
            schema.node_labels?.forEach(label => {
              suggestions.push({
                label: `:${label}`,
                kind: monaco.languages.CompletionItemKind.Class,
                insertText: `:${label}`,
                range: range,
                detail: '节点标签'
              })
            })

            // 关系类型
            schema.relationship_types?.forEach(relType => {
              suggestions.push({
                label: `:${relType}`,
                kind: monaco.languages.CompletionItemKind.Interface,
                insertText: `:${relType}`,
                range: range,
                detail: '关系类型'
              })
            })

            // 属性键
            schema.property_keys?.forEach(propKey => {
              suggestions.push({
                label: propKey,
                kind: monaco.languages.CompletionItemKind.Property,
                insertText: propKey,
                range: range,
                detail: '属性键'
              })
            })
          }

          // 常用查询模板
          const templates = [
            {
              label: 'MATCH pattern',
              insertText: 'MATCH (n) RETURN n LIMIT 25',
              detail: '基础匹配模式'
            },
            {
              label: 'MATCH relationship',
              insertText: 'MATCH (n)-[r]-(m) RETURN n, r, m LIMIT 25',
              detail: '关系匹配模式'
            },
            {
              label: 'CREATE node',
              insertText: 'CREATE (n:${1:Label} {${2:property}: \'${3:value}\'})',
              detail: '创建节点'
            },
            {
              label: 'MERGE node',
              insertText: 'MERGE (n:${1:Label} {${2:property}: \'${3:value}\'})',
              detail: '合并节点'
            }
          ]

          templates.forEach(template => {
            suggestions.push({
              label: template.label,
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: template.insertText,
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              range: range,
              detail: template.detail
            })
          })

          return { suggestions }
        }
      })
    }

    // 设置编辑器选项
    editor.updateOptions({
      fontSize: 13,
      lineHeight: 20,
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      lineNumbers: 'on',
      folding: false,
      lineDecorationsWidth: 0,
      lineNumbersMinChars: 3,
      renderLineHighlight: 'line',
      selectOnLineNumbers: true,
      automaticLayout: true
    })

    // 添加快捷键
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
      onExecute?.()
    })

    // 设置主题
    monaco.editor.setTheme('vs')
  }

  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined) {
      onChange(value)
    }
  }

  return (
    <div className="border border-gray-300 dark:border-gray-600 rounded-md overflow-hidden relative">
      <Editor
        height={height}
        defaultLanguage="cypher"
        value={value}
        onChange={handleEditorChange}
        onMount={handleEditorMount}
        options={{
          readOnly: readonly,
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
          wordWrap: 'on',
          lineNumbers: 'on',
          fontSize: 13,
          lineHeight: 20,
          folding: false,
          renderLineHighlight: 'line',
          selectOnLineNumbers: true,
          automaticLayout: true,
          tabSize: 2,
          insertSpaces: true,
          trimAutoWhitespace: true,
          formatOnPaste: true,
          formatOnType: true
        }}
        theme="vs"
      />
    </div>
  )
} 