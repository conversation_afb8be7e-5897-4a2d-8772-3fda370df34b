import * as React from "react"
import { X } from "lucide-react"
import { cn } from "@/lib/utils"

// Toast 上下文
interface ToastContextType {
  showToast: (message: string, type?: 'info' | 'success' | 'warning' | 'error', duration?: number) => void
}

const ToastContext = React.createContext<ToastContextType | undefined>(undefined)

// Toast 项目接口
interface ToastItem {
  id: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  duration: number
}

// Toast Provider 组件
export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = React.useState<ToastItem[]>([])

  const showToast = React.useCallback((
    message: string, 
    type: 'info' | 'success' | 'warning' | 'error' = 'info', 
    duration: number = 3000
  ) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newToast: ToastItem = { id, message, type, duration }
    
    setToasts(prev => [...prev, newToast])
    
    // 自动移除
    setTimeout(() => {
      setToasts(prev => prev.filter(toast => toast.id !== id))
    }, duration)
  }, [])

  const removeToast = React.useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }, [])

  return (
    <ToastContext.Provider value={{ showToast }}>
      {children}
      
      {/* Toast 容器 */}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        {toasts.map((toast) => (
          <Toast
            key={toast.id}
            toast={toast}
            onRemove={() => removeToast(toast.id)}
          />
        ))}
      </div>
    </ToastContext.Provider>
  )
}

// Toast 组件
interface ToastProps {
  toast: ToastItem
  onRemove: () => void
}

function Toast({ toast, onRemove }: ToastProps) {
  const getToastStyles = (type: ToastItem['type']) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800'
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800'
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800'
    }
  }

  const getIcon = (type: ToastItem['type']) => {
    switch (type) {
      case 'success':
        return '✓'
      case 'warning':
        return '⚠'
      case 'error':
        return '✕'
      default:
        return 'ℹ'
    }
  }

  return (
    <div
      className={cn(
        "flex items-center justify-between p-3 rounded-lg border shadow-lg min-w-[300px] max-w-[400px] animate-in slide-in-from-right-full duration-300",
        getToastStyles(toast.type)
      )}
    >
      <div className="flex items-center space-x-2">
        <span className="text-sm font-medium">
          {getIcon(toast.type)}
        </span>
        <span className="text-sm">
          {toast.message}
        </span>
      </div>
      
      <button
        onClick={onRemove}
        className="ml-3 text-current opacity-70 hover:opacity-100 transition-opacity"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  )
}

// Hook 使用 Toast
export function useToast() {
  const context = React.useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context
}

// 导出类型
export type { ToastItem }
