

interface FilterOption {
  value: string
  label: string
  count?: number
  color?: string
}

interface TagFilterProps {
  title: string
  options: FilterOption[]
  selectedValues: string[]
  onSelectionChange: (selectedValues: string[]) => void
  className?: string
}

export function TagFilter({
  title,
  options,
  selectedValues,
  onSelectionChange,
  className = ""
}: TagFilterProps) {

  // 处理标签点击
  const handleTagClick = (value: string) => {
    if (value === 'all') {
      // 点击"全部"标签
      if (selectedValues.includes('all')) {
        // 如果已选中"全部"，则取消所有选择
        onSelectionChange([])
      } else {
        // 如果未选中"全部"，则只选择"全部"
        onSelectionChange(['all'])
      }
    } else {
      // 点击具体标签
      if (selectedValues.includes(value)) {
        // 如果已选中，则取消选择
        const newSelection = selectedValues.filter(v => v !== value && v !== 'all')
        onSelectionChange(newSelection)
      } else {
        // 如果未选中，则添加选择（同时移除"全部"）
        const newSelection = [...selectedValues.filter(v => v !== 'all'), value]
        onSelectionChange(newSelection)
      }
    }
  }

  return (
    <div className={`${className}`}>
      <div className="mb-2">
        <h3 className="text-xs font-medium text-gray-700 dark:text-gray-300">
          {title}
        </h3>
      </div>

      <div className="flex flex-wrap gap-1.5 items-center">
        {options.map(option => {
          const isSelected = selectedValues.includes(option.value)
          const isAllTag = option.value === 'all'

          return (
            <button
              key={option.value}
              onClick={() => handleTagClick(option.value)}
              className={`
                inline-flex items-center gap-1 px-2 py-1 text-xs font-medium
                transition-all duration-200 border rounded
                ${isSelected
                  ? isAllTag
                    ? 'bg-blue-600 text-white border-blue-600 shadow-sm'
                    : 'bg-blue-50 text-blue-700 border-blue-300 shadow-sm dark:bg-blue-900/50 dark:text-blue-200 dark:border-blue-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-500'
                }
                hover:shadow-sm active:scale-95 cursor-pointer
                ${isAllTag ? 'font-semibold' : ''}
              `}
            >
              {/* "全部"标签的特殊图标 */}
              {isAllTag && (
                <svg className="w-2.5 h-2.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
              )}

              {/* 颜色指示器（仅非"全部"标签显示） */}
              {!isAllTag && option.color && (
                <div
                  className="w-2 h-2 rounded-full border border-white/20"
                  style={{ backgroundColor: option.color }}
                />
              )}

              {/* 标签文本 */}
              <span>{option.label}</span>

              {/* 数量显示 */}
              {option.count !== undefined && (
                <span className={`
                  text-xs px-1.5 py-0.5 rounded font-medium min-w-[16px] text-center
                  ${isSelected
                    ? isAllTag
                      ? 'bg-blue-500 text-white'
                      : 'bg-blue-200 text-blue-800 dark:bg-blue-800 dark:text-blue-200'
                    : 'bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                  }
                `}>
                  {option.count}
                </span>
              )}
            </button>
          )
        })}
      </div>

      {/* 选中状态提示 */}
      {selectedValues.length > 0 && !selectedValues.includes('all') && (
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
          <svg className="w-2.5 h-2.5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          已选择 {selectedValues.length} 项
        </div>
      )}

      {selectedValues.includes('all') && (
        <div className="mt-2 text-xs text-blue-600 dark:text-blue-400 flex items-center gap-1 font-medium">
          <svg className="w-2.5 h-2.5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          已选择全部
        </div>
      )}
    </div>
  )
}
