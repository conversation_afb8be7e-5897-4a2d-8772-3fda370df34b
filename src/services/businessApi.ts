import { httpClient, httpClientRaw } from '@/lib/httpClient'

// 节点管理相关 API
export const nodeApi = {
  // 区域节点
  areas: {
    search: (params?: { name?: string; code?: string; level?: number; parent_code?: string; limit?: number }) =>
      httpClient.get('/nodes/areas/', { params }),
    getByCode: (code: string) =>
      httpClient.get(`/nodes/areas/${code}`),
    getStatistics: () =>
      httpClient.get('/nodes/areas/statistics'),
    healthCheck: () =>
      httpClient.get('/nodes/areas/health'),
    import: (params?: { clear_existing?: boolean }) =>
      httpClient.post('/nodes/areas/import', null, { params }),
    deleteAll: () =>
      httpClient.delete('/nodes/areas/')
  },

  // 人员节点
  persons: {
    // 获取所有人员类型概览
    getOverview: () =>
      httpClient.get('/nodes/persons/'),
    
    // 健康检查
    healthCheck: () =>
      httpClient.get('/nodes/persons/health'),

    // 代理人
    agents: {
      search: (params?: { name?: string; code?: string; phone?: string; city?: string; status?: string; limit?: number }) =>
        httpClient.get('/nodes/persons/agents/', { params }),
      getByCode: (code: string) =>
        httpClient.get(`/nodes/persons/agents/${code}`),
      getStatistics: () =>
        httpClient.get('/nodes/persons/agents/statistics'),
      import: (params?: { clear_existing?: boolean }) =>
        httpClient.post('/nodes/persons/agents/import', null, { params }),
      deleteAll: () =>
        httpClient.delete('/nodes/persons/agents/')
    },

    // BD
    bd: {
      search: (params?: { name?: string; code?: string; phone?: string; city?: string; status?: string; limit?: number }) =>
        httpClient.get('/nodes/persons/bd/', { params }),
      getByCode: (code: string) =>
        httpClient.get(`/nodes/persons/bd/${code}`),
      getStatistics: () =>
        httpClient.get('/nodes/persons/bd/statistics'),
      import: (params?: { clear_existing?: boolean }) =>
        httpClient.post('/nodes/persons/bd/import', null, { params }),
      deleteAll: () =>
        httpClient.delete('/nodes/persons/bd/')
    },

    // MO
    mo: {
      search: (params?: { name?: string; code?: string; phone?: string; city?: string; status?: string; limit?: number }) =>
        httpClient.get('/nodes/persons/mo/', { params }),
      getByCode: (code: string) =>
        httpClient.get(`/nodes/persons/mo/${code}`),
      getStatistics: () =>
        httpClient.get('/nodes/persons/mo/statistics'),
      import: (params?: { clear_existing?: boolean }) =>
        httpClient.post('/nodes/persons/mo/import', null, { params }),
      deleteAll: () =>
        httpClient.delete('/nodes/persons/mo/')
    },

    // 被保险人
    insured: {
      search: (params?: { name?: string; code?: string; phone?: string; city?: string; status?: string; limit?: number }) =>
        httpClient.get('/nodes/persons/insured/', { params }),
      getByCode: (code: string) =>
        httpClient.get(`/nodes/persons/insured/${code}`),
      getStatistics: () =>
        httpClient.get('/nodes/persons/insured/statistics'),
      import: (params?: { clear_existing?: boolean }) =>
        httpClient.post('/nodes/persons/insured/import', null, { params }),
      deleteAll: () =>
        httpClient.delete('/nodes/persons/insured/')
    }
  },

  // 组织节点
  organizations: {
    // 获取所有组织类型概览
    getOverview: () =>
      httpClient.get('/nodes/organizations/'),
    
    // 健康检查
    healthCheck: () =>
      httpClient.get('/nodes/organizations/health'),

    // 保险公司
    insuranceCompanies: {
      search: (params?: { name?: string; code?: string; province?: string; city?: string; limit?: number }) =>
        httpClient.get('/nodes/organizations/insurance-companies/', { params }),
      getByCode: (code: string) =>
        httpClient.get(`/nodes/organizations/insurance-companies/${code}`),
      getStatistics: () =>
        httpClient.get('/nodes/organizations/insurance-companies/statistics'),
      import: (params?: { clear_existing?: boolean }) =>
        httpClient.post('/nodes/organizations/insurance-companies/import', null, { params }),
      deleteAll: () =>
        httpClient.delete('/nodes/organizations/insurance-companies/')
    },

    // 经纪公司
    brokerageCompanies: {
      search: (params?: { name?: string; code?: string; province?: string; city?: string; limit?: number }) =>
        httpClient.get('/nodes/organizations/brokerage-companies/', { params }),
      getByCode: (code: string) =>
        httpClient.get(`/nodes/organizations/brokerage-companies/${code}`),
      getStatistics: () =>
        httpClient.get('/nodes/organizations/brokerage-companies/statistics'),
      import: (params?: { clear_existing?: boolean }) =>
        httpClient.post('/nodes/organizations/brokerage-companies/import', null, { params }),
      deleteAll: () =>
        httpClient.delete('/nodes/organizations/brokerage-companies/')
    },

    // 销售渠道
    salesChannels: {
      search: (params?: { name?: string; code?: string; province?: string; city?: string; limit?: number }) =>
        httpClient.get('/nodes/organizations/sales-channels/', { params }),
      getByCode: (code: string) =>
        httpClient.get(`/nodes/organizations/sales-channels/${code}`),
      getStatistics: () =>
        httpClient.get('/nodes/organizations/sales-channels/statistics'),
      import: (params?: { clear_existing?: boolean }) =>
        httpClient.post('/nodes/organizations/sales-channels/import', null, { params }),
      deleteAll: () =>
        httpClient.delete('/nodes/organizations/sales-channels/')
    },

    // 投保公司
    policyCompanies: {
      search: (params?: { name?: string; code?: string; province?: string; city?: string; limit?: number }) =>
        httpClient.get('/nodes/organizations/policy-companies/', { params }),
      getByCode: (code: string) =>
        httpClient.get(`/nodes/organizations/policy-companies/${code}`),
      getStatistics: () =>
        httpClient.get('/nodes/organizations/policy-companies/statistics'),
      import: (params?: { clear_existing?: boolean }) =>
        httpClient.post('/nodes/organizations/policy-companies/import', null, { params }),
      deleteAll: () =>
        httpClient.delete('/nodes/organizations/policy-companies/')
    }
  },

  // 保单节点
  policies: {
    search: (params?: { 
      policy_code?: string; 
      insured_name?: string; 
      insurance_code?: string; 
      product_code?: string; 
      policy_status?: string; 
      start_date?: string; 
      end_date?: string; 
      limit?: number 
    }) =>
      httpClient.get('/nodes/policies/', { params }),
    getByCode: (policyCode: string) =>
      httpClient.get(`/nodes/policies/${policyCode}`),
    getStatistics: () =>
      httpClient.get('/nodes/policies/statistics'),
    healthCheck: () =>
      httpClient.get('/nodes/policies/health'),
    import: (params?: { clear_existing?: boolean; limit?: number; where_conditions?: string; custom_day?: string }) =>
      httpClient.post('/nodes/policies/import', null, { params }),
    deleteAll: () =>
      httpClient.delete('/nodes/policies/')
  },

  // 产品节点
  products: {
    search: (params?: {
      product_code?: string;
      product_name?: string;
      insurance_code?: string;
      product_type?: string;
      status?: string;
      limit?: number;
    }) =>
      httpClient.get('/nodes/products/', { params }),
    getByCode: (productCode: string) =>
      httpClient.get(`/nodes/products/${productCode}`),
    getStatistics: () =>
      httpClient.get('/nodes/products/statistics'),
    healthCheck: () =>
      httpClient.get('/nodes/products/health'),
    import: (params?: { clear_existing?: boolean; limit?: number; where_conditions?: string; custom_day?: string }) =>
      httpClient.post('/nodes/products/import', null, { params }),
    deleteAll: () =>
      httpClient.delete('/nodes/products/')
  }
}

// 关系管理相关 API
export const relationshipApi = {
  // 地理位置关系
  geographic: {
    // 获取所有地理关系类型概览
    getOverview: () =>
      httpClient.get('/relationships/geographic/'),
    
    // 健康检查
    healthCheck: () =>
      httpClient.get('/relationships/geographic/health'),

    // 区域层级关系
    areaHierarchy: {
      search: (params?: { source_code?: string; target_code?: string; area_name?: string; province?: string; city?: string; include_details?: boolean; limit?: number }) =>
        httpClient.get('/relationships/geographic/area-hierarchy/', { params }),
      getStatistics: () =>
        httpClient.get('/relationships/geographic/area-hierarchy/statistics'),
      extract: (params?: { clear_existing?: boolean; limit?: number }) =>
        httpClient.post('/relationships/geographic/area-hierarchy/extract', null, { params }),
      deleteAll: () =>
        httpClient.delete('/relationships/geographic/area-hierarchy/')
    },

    // 经纪公司-区域关系
    brokerageCompanyArea: {
      search: (params?: { source_code?: string; target_code?: string; area_name?: string; province?: string; city?: string; include_details?: boolean; limit?: number }) =>
        httpClient.get('/relationships/geographic/brokerage-company-area/', { params }),
      getStatistics: () =>
        httpClient.get('/relationships/geographic/brokerage-company-area/statistics'),
      extract: (params?: { clear_existing?: boolean; limit?: number }) =>
        httpClient.post('/relationships/geographic/brokerage-company-area/extract', null, { params }),
      deleteAll: () =>
        httpClient.delete('/relationships/geographic/brokerage-company-area/')
    },

    // 理赔-区域关系
    claimsArea: {
      search: (params?: { source_code?: string; target_code?: string; area_name?: string; province?: string; city?: string; include_details?: boolean; limit?: number }) =>
        httpClient.get('/relationships/geographic/claims-area/', { params }),
      getStatistics: () =>
        httpClient.get('/relationships/geographic/claims-area/statistics'),
      extract: (params?: { clear_existing?: boolean; limit?: number }) =>
        httpClient.post('/relationships/geographic/claims-area/extract', null, { params }),
      deleteAll: () =>
        httpClient.delete('/relationships/geographic/claims-area/')
    },

    // 投保公司-区域关系
    policyCompanyArea: {
      search: (params?: { source_code?: string; target_code?: string; area_name?: string; province?: string; city?: string; include_details?: boolean; limit?: number }) =>
        httpClient.get('/relationships/geographic/policy-company-area/', { params }),
      getStatistics: () =>
        httpClient.get('/relationships/geographic/policy-company-area/statistics'),
      extract: (params?: { clear_existing?: boolean; limit?: number }) =>
        httpClient.post('/relationships/geographic/policy-company-area/extract', null, { params }),
      deleteAll: () =>
        httpClient.delete('/relationships/geographic/policy-company-area/')
    },

    // 销售渠道-区域关系
    salesChannelArea: {
      search: (params?: { source_code?: string; target_code?: string; area_name?: string; province?: string; city?: string; include_details?: boolean; limit?: number }) =>
        httpClient.get('/relationships/geographic/sales-channel-area/', { params }),
      getStatistics: () =>
        httpClient.get('/relationships/geographic/sales-channel-area/statistics'),
      extract: (params?: { clear_existing?: boolean; limit?: number }) =>
        httpClient.post('/relationships/geographic/sales-channel-area/extract', null, { params }),
      deleteAll: () =>
        httpClient.delete('/relationships/geographic/sales-channel-area/')
    }
  },

  // 业务关系
  business: {
    search: (params: {
      relation_type: 'policy_product' | 'insured_policy' | 'policy_salesperson';
      policy_code?: string;
      product_code?: string;
      person_id?: string;
      include_node_details?: boolean;
      limit?: number;
    }) =>
      httpClient.get('/relationships/business/', { params }),
    
    getStatistics: (params?: { relation_type?: 'policy_product' | 'insured_policy' | 'policy_salesperson' }) =>
      httpClient.get('/relationships/business/statistics', { params }),
    
    healthCheck: () =>
      httpClient.get('/relationships/business/health'),
    
    import: (params: {
      relation_type: 'policy_product' | 'insured_policy' | 'policy_salesperson';
      clear_existing?: boolean;
      limit?: number;
      where_conditions?: string;
      custom_day?: string;
    }) =>
      httpClient.post('/relationships/business/import', null, { params }),
    
    delete: (params: { relation_type: 'policy_product' | 'insured_policy' | 'policy_salesperson' }) =>
      httpClient.delete('/relationships/business/', { params })
  }
}

// 系统健康检查相关 API
export const systemApi = {
  // 基础健康检查 - 不带/api/v1前缀
  healthCheck: () =>
    httpClientRaw.get('/health'),

  // 系统整体健康检查
  systemHealthCheck: () =>
    httpClient.get('/system/health/'),

  // 数据库健康检查
  databaseHealthCheck: () =>
    httpClient.get('/system/health/database'),

  // 服务组件健康检查
  servicesHealthCheck: () =>
    httpClient.get('/system/health/services'),

  // 详细健康检查报告
  detailedHealthCheck: () =>
    httpClient.get('/system/health/detailed')
}

// 导出默认对象（保持向后兼容）
export default {
  nodeApi,
  relationshipApi,
  systemApi
}
