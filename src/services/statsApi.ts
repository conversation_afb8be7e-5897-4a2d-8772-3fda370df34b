import { graph<PERSON>pi } from './graphApi'
import { nodeApi, relationshipApi } from './businessApi'

// 统计相关 API
export const statsApi = {
  // 获取整体统计
  getOverallStats: () =>
    graphApi.getOverview(),

  // 获取节点统计
  getNodeStats: async () => {
    try {
      const [areasStats, personsOverview, orgsOverview, policiesStats, productsStats] = await Promise.all([
        nodeApi.areas.getStatistics(),
        nodeApi.persons.getOverview(),
        nodeApi.organizations.getOverview(),
        nodeApi.policies.getStatistics(),
        nodeApi.products.getStatistics()
      ])

      return {
        areas: areasStats,
        persons: personsOverview,
        organizations: orgsOverview,
        policies: policiesStats,
        products: productsStats
      }
    } catch (error) {
      console.error('获取节点统计失败:', error)
      return {}
    }
  },

  // 获取关系统计
  getRelationshipStats: async () => {
    try {
      const [geoOverview, businessStats] = await Promise.all([
        relationshipApi.geographic.getOverview(),
        relationshipApi.business.getStatistics()
      ])

      return {
        geographic: geoOverview,
        business: businessStats
      }
    } catch (error) {
      console.error('获取关系统计失败:', error)
      return {}
    }
  }
} 