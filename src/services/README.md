# 服务层架构说明

## 架构重构

我们已经对API服务层进行了重构，消除了重复代码，并引入了工具类来提高代码复用性。

## 文件结构

```
src/services/
├── api.ts              # API主入口文件，重新导出所有模块
├── graphApi.ts         # 图谱查询相关API
├── businessApi.ts      # 后端业务服务API
├── statsApi.ts         # 统计相关API
└── README.md          # 本文档

src/lib/
├── httpClient.ts       # 统一的HTTP客户端工具类
├── apiUtils.ts         # API相关工具函数
└── utils.ts           # 通用工具函数

src/hooks/
├── useApiRequest.ts    # 基于ahooks的API请求hooks
└── useClickHandler.ts  # 点击处理hooks
```

## 核心改进

### 1. 统一HTTP客户端 (`src/lib/httpClient.ts`)

- **消除重复**：所有API文件之前都有相同的axios配置代码
- **统一管理**：拦截器、错误处理、超时配置等都在一个地方管理
- **类型安全**：提供了完整的TypeScript类型定义

```typescript
// 使用方式
import { httpClient } from '@/lib/httpClient'

// GET请求
const data = await httpClient.get('/api/endpoint')

// POST请求
const result = await httpClient.post('/api/endpoint', { data })
```

### 2. API工具函数 (`src/lib/apiUtils.ts`)

提取了常用的API操作工具函数：

- `formatNodeLabels()` - 格式化节点标签
- `enhanceCypherQuery()` - 增强Cypher查询
- `buildRelationPattern()` - 构建关系模式
- `buildSetClause()` - 构建SET子句
- `buildPropertiesString()` - 构建属性字符串
- `buildRelationshipTypeFilter()` - 构建关系类型过滤器
- `handleApiError()` - 统一错误处理

### 3. 基于ahooks的请求Hooks (`src/hooks/useApiRequest.ts`)

使用ahooks提供的工具来简化API调用：

- `useApiRequest()` - 通用API请求hook，支持防抖、错误处理
- `useNodeDetails()` - 节点详情查询hook
- `useNodeNeighbors()` - 节点邻居查询hook
- `useCypherQuery()` - Cypher查询hook

```typescript
// 使用示例
const { data, loading, error, run } = useNodeDetails(nodeId, nodeLabels)

// 带防抖的搜索
const searchHook = useApiRequest(searchService, { debounceWait: 300 })
```

## 使用指南

### 导入API

```typescript
// 推荐：按需导入
import { graphApi, nodeApi } from '@/services/api'

// 或者直接从具体模块导入
import { neo4jApi } from '@/services/graphApi'
```

### 使用HTTP客户端

```typescript
import { httpClient } from '@/lib/httpClient'

// 直接使用
const response = await httpClient.get('/endpoint')
```

### 使用工具函数

```typescript
import { formatNodeLabels, buildRelationPattern } from '@/lib/apiUtils'

const label = formatNodeLabels('Organization:Company')
const pattern = buildRelationPattern('out', 2)
```

### 使用请求Hooks

```typescript
import { useNodeDetails, useCypherQuery } from '@/hooks/useApiRequest'

function NodeComponent({ nodeId, nodeLabels }) {
  const { data, loading, error } = useNodeDetails(nodeId, nodeLabels)
  
  if (loading) return <div>加载中...</div>
  if (error) return <div>错误: {error.message}</div>
  
  return <div>{JSON.stringify(data)}</div>
}
```

## 性能优化

1. **减少重复代码**：所有axios配置只在一个地方定义
2. **函数复用**：常用的Cypher构建逻辑提取为工具函数
3. **防抖处理**：使用ahooks的useDebounce避免频繁请求
4. **缓存机制**：useRequest自带缓存和重复请求取消
5. **错误处理**：统一的错误处理和上报机制

## 迁移指南

如果你有现有代码需要迁移：

1. **替换axios实例**：将 `api.get()` 改为 `httpClient.get()`
2. **使用工具函数**：将重复的Cypher构建逻辑替换为工具函数调用
3. **采用Hooks**：在React组件中使用我们提供的API hooks
4. **统一导入**：从 `@/services/api` 统一导入所需的API模块

## 扩展建议

1. **添加更多工具函数**：根据业务需要继续提取重复逻辑
2. **增强错误处理**：可以添加错误上报、用户提示等功能
3. **缓存策略**：可以根据业务需求配置不同的缓存策略
4. **请求拦截**：可以在httpClient中添加认证、日志等功能 