import { neo4j<PERSON>pi } from './graphApi'
import { colorManager } from '@/lib/colorManager'

// 全局初始化状态
let isInitialized = false
let initializationPromise: Promise<void> | null = null

// 全局初始化服务
export const globalInitService = {
  // 检查是否已初始化
  isInitialized: () => isInitialized,

  // 获取初始化Promise（用于等待初始化完成）
  getInitializationPromise: () => initializationPromise,

  // 执行全局初始化
  initialize: async (): Promise<void> => {
    // 如果已经初始化或正在初始化，直接返回
    if (isInitialized) {
      return Promise.resolve()
    }
    
    if (initializationPromise) {
      return initializationPromise
    }

    // 创建初始化Promise
    initializationPromise = performInitialization()
    
    try {
      await initializationPromise
      isInitialized = true
    } catch (error) {
      // 初始化失败，重置状态以便重试
      initializationPromise = null
      throw error
    }
  },

  // 强制重新初始化
  reinitialize: async (): Promise<void> => {
    isInitialized = false
    initializationPromise = null
    return globalInitService.initialize()
  }
}

// 执行实际的初始化逻辑
async function performInitialization(): Promise<void> {
  try {
    console.log('🚀 开始全局初始化...')
    
    // 获取Schema信息并初始化颜色映射
    const schemaData = await neo4jApi.getSchema()
    
    if (schemaData.labels && Array.isArray(schemaData.labels)) {
      console.log('🎨 初始化颜色管理器，节点类型数量:', schemaData.labels.length)
      
      // 初始化颜色映射
      colorManager.initializeFromSchema(schemaData.labels)
      
      console.log('✅ 全局初始化完成')
    } else {
      console.warn('⚠️ Schema数据格式异常，跳过颜色初始化')
    }
  } catch (error) {
    console.error('❌ 全局初始化失败:', error)
    throw error
  }
}

// 确保初始化完成的工具函数
export async function ensureInitialized(): Promise<void> {
  if (!globalInitService.isInitialized()) {
    await globalInitService.initialize()
  }
}

// 导出默认实例
export default globalInitService
