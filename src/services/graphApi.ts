import { httpClient } from '@/lib/httpClient'
import { 
  formatNodeLabels, 
  enhanceCypherQuery, 
  buildRelationPattern,
  buildSetClause,
  buildPropertiesString,

} from '@/lib/apiUtils'

// Neo4j查询接口
export const neo4jApi = {
  // 执行Cypher查询
  executeQuery: (data: { 
    query: string; 
    parameters?: Record<string, any>; 
    response_format?: 'standard' | 'reference' 
  }) =>
    httpClient.post('/neo4j/query', data),

  // 获取数据库模式信息
  getSchema: () =>
    httpClient.get('/neo4j/schema'),

  // 获取数据库统计信息
  getStatistics: () =>
    httpClient.get('/neo4j/statistics'),

  // 获取数据库样本数据
  getSample: (params?: { limit?: number }) =>
    httpClient.get('/neo4j/quick/sample', { params }),
}

// 图谱可视化相关 API (基于Neo4j接口的封装)
export const graphApi = {
  // 获取图概览统计
  getOverview: () =>
    neo4jApi.getStatistics(),

  // 获取节点样本用于初始显示
  getNodeSamples: (params?: { limit?: number }) =>
    neo4jApi.getSample(params),

  // 获取关系样本
  getRelationshipSamples: (params?: { limit?: number }) =>
    neo4jApi.executeQuery({
      query: `MATCH (n)-[r]->(m) RETURN n, r, m, labels(n) as n_labels, labels(m) as m_labels LIMIT ${params?.limit || 50}`,
      response_format: 'reference'
    }),

  // 执行 Cypher 查询
  executeCypher: (data: { 
    query: string; 
    params?: Record<string, any>; 
    include_stats?: boolean; 
    limit?: number 
  }) =>
    neo4jApi.executeQuery({
      query: data.query,
      parameters: data.params,
      response_format: 'reference'
    }),

  // 验证 Cypher 语法 (通过尝试执行EXPLAIN来验证)
  validateCypher: (data: { query: string }) =>
    neo4jApi.executeQuery({
      query: `EXPLAIN ${data.query}`,
      response_format: 'reference'
    }),

  // 获取子图数据
  getSubgraph: (data: { cypher: string; layout?: string; include_styling?: boolean }) =>
    neo4jApi.executeQuery({
      query: data.cypher,
      response_format: 'reference'
    }),

  // 搜索节点
  searchNodes: (params: { 
    query: string; 
    labels?: string; 
    properties?: string; 
    limit?: number 
  }) => {
    const limit = params.limit || 50
    let cypherQuery = ''
    
    if (params.labels) {
      cypherQuery = `MATCH (n:${params.labels}) WHERE n.name CONTAINS '${params.query}' OR n.code CONTAINS '${params.query}' RETURN n, labels(n) as n_labels LIMIT ${limit}`
    } else {
      cypherQuery = `MATCH (n) WHERE n.name CONTAINS '${params.query}' OR n.code CONTAINS '${params.query}' RETURN n, labels(n) as n_labels LIMIT ${limit}`
    }
    
    return neo4jApi.executeQuery({
      query: cypherQuery,
      response_format: 'reference'
    })
  },

  // 获取节点详情
  getNodeDetails: (nodeId: string | number, nodeLabels?: string) => {
    const label = formatNodeLabels(nodeLabels)
    
    return neo4jApi.executeQuery({
      query: `MATCH (n${label} {business_id: '${nodeId}'}) RETURN n, labels(n) as n_labels`,
      response_format: 'reference'
    })
  },

  // 获取节点邻居
  getNodeNeighbors: (nodeId: string | number, nodeLabels?: string, params?: { 
    depth?: number; 
    direction?: 'in' | 'out' | 'both'; 
    limit?: number; 
    [key: string]: any
  }) => {
    const depth = params?.depth || 1
    const limit = params?.limit || 20
    const label = formatNodeLabels(nodeLabels)
    const relationPattern = buildRelationPattern(params?.direction, depth)
    

    
    return neo4jApi.executeQuery({
      query: `MATCH (n${label} {business_id: '${nodeId}'})${relationPattern}(m) RETURN n, r, m, labels(n) as n_labels, labels(m) as m_labels LIMIT ${limit}`,
      response_format: 'reference'
    })
  },

  // 获取节点标签
  getNodeLabels: () =>
    neo4jApi.getSchema(),

  // 获取关系类型
  getRelationshipTypes: () =>
    neo4jApi.getSchema(),

  // 最短路径查询
  getShortestPath: (data: {
    start_node_id: string | number;
    end_node_id: string | number;
    max_depth?: number;
    relationship_types?: string[]
  }) => {
    const maxDepth = data.max_depth || 5

    // 构建关系模式
    let relationshipPattern = ''
    if (!data.relationship_types || data.relationship_types.length === 0) {
      relationshipPattern = `[*1..${maxDepth}]`
    } else {
      const relTypes = data.relationship_types.map(t => `:${t}`).join('|')
      relationshipPattern = `[${relTypes}*1..${maxDepth}]`
    }

    return neo4jApi.executeQuery({
      query: `MATCH (start {business_id: '${data.start_node_id}'}), (end {business_id: '${data.end_node_id}'})
              MATCH p = shortestPath((start)-${relationshipPattern}-(end))
              RETURN p, nodes(p) as path_nodes, [n in nodes(p) | labels(n)] as path_labels`,
      response_format: 'reference'
    })
  },

  // 节点度分析
  getNodeDegreeAnalysis: (params?: { limit?: number }) => {
    const limit = params?.limit || 50
    return neo4jApi.executeQuery({
      query: `MATCH (n) 
              OPTIONAL MATCH (n)-[r]-() 
              RETURN n, labels(n) as n_labels, count(r) as degree 
              ORDER BY degree DESC 
              LIMIT ${limit}`,
      response_format: 'reference'
    })
  },

  // 创建节点 (暂时保留原接口，实际使用需要根据业务需求调整)
  createNode: (data: { labels: string[]; properties?: Record<string, any> }) => {
    const labels = data.labels.join(':')
    const props = buildPropertiesString(data.properties || {})
    
    return neo4jApi.executeQuery({
      query: `CREATE (n:${labels} ${props}) RETURN n, labels(n) as n_labels`,
      response_format: 'reference'
    })
  },

  // 更新节点
  updateNode: (nodeId: string | number, nodeLabels?: string, properties?: Record<string, any>) => {
    const label = formatNodeLabels(nodeLabels)
    const setClause = properties ? buildSetClause(properties) : ''
    
    return neo4jApi.executeQuery({
      query: `MATCH (n${label} {business_id: '${nodeId}'}) SET ${setClause} RETURN n, labels(n) as n_labels`,
      response_format: 'reference'
    })
  },

  // 删除节点
  deleteNode: (nodeId: string | number, nodeLabels?: string) => {
    const label = formatNodeLabels(nodeLabels)
    
    return neo4jApi.executeQuery({
      query: `MATCH (n${label} {business_id: '${nodeId}'}) DETACH DELETE n`,
      response_format: 'reference'
    })
  },

  // 创建关系
  createRelationship: (data: { 
    from_node_id: string | number; 
    to_node_id: string | number; 
    type: string; 
    properties?: Record<string, any> 
  }) => {
    const props = buildPropertiesString(data.properties || {})
    
    return neo4jApi.executeQuery({
      query: `MATCH (a {business_id: '${data.from_node_id}'}), (b {business_id: '${data.to_node_id}'}) 
              CREATE (a)-[r:${data.type} ${props}]->(b) 
              RETURN a, b, r, labels(a) as a_labels, labels(b) as b_labels`,
      response_format: 'reference'
    })
  },

  // 获取关系详情
  getRelationshipDetails: (relationshipId: string | number) =>
    neo4jApi.executeQuery({
      query: `MATCH (a)-[r]->(b) WHERE id(r) = ${relationshipId} RETURN a, r, b, labels(a) as a_labels, labels(b) as b_labels`,
      response_format: 'reference'
    }),

  // 删除关系
  deleteRelationship: (relationshipId: string | number) =>
    neo4jApi.executeQuery({
      query: `MATCH ()-[r]->() WHERE id(r) = ${relationshipId} DELETE r`,
      response_format: 'reference'
    }),

  // 导出查询结果
  exportResults: (params: { query: string; format?: 'json' | 'csv'; limit?: number }) => {
    const enhancedQuery = enhanceCypherQuery(params.query)
    
    return neo4jApi.executeQuery({
      query: enhancedQuery,
      response_format: 'reference'
    })
  },
} 