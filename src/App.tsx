import { useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { StagewiseToolbar } from '@stagewise/toolbar-react'
import { ReactPlugin } from '@stagewise-plugins/react'
import { Layout } from '@/components/layout/Layout'
import { Dashboard } from '@/pages/Dashboard'
import { GraphAnalysis } from '@/pages/GraphAnalysis'
import { NodeManagement } from '@/pages/NodeManagement'
import { RelationshipAnalysis } from '@/pages/RelationshipAnalysis'
import { Settings } from '@/pages/Settings'
import { globalInitService } from '@/services/globalInitService'
import { ToastProvider } from '@/components/ui/toast'

function App() {
  // 应用启动时执行全局初始化
  useEffect(() => {
    globalInitService.initialize().catch(error => {
      console.error('应用初始化失败:', error)
    })
  }, [])

  return (
    <ToastProvider>
      <StagewiseToolbar
        config={{
          plugins: [ReactPlugin]
        }}
      />
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/graph" element={<GraphAnalysis />} />
            <Route path="/nodes" element={<NodeManagement />} />
            <Route path="/relationships" element={<RelationshipAnalysis />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </Layout>
      </Router>
    </ToastProvider>
  )
}

export default App 