declare module 'cytoscape-navigator' {
  import { Core } from 'cytoscape'

  interface NavigatorOptions {
    container?: string | false | undefined
    viewLiveFramerate?: number | false
    thumbnailEventFramerate?: number
    thumbnailLiveFramerate?: number | false
    dblClickDelay?: number
    removeCustomContainer?: boolean
    rerenderDelay?: number
  }

  interface NavigatorInstance {
    destroy(): void
  }

  function navigator(cytoscape: any): void

  export = navigator
}

declare module 'cytoscape' {
  interface Core {
    navigator(options?: import('cytoscape-navigator').NavigatorOptions): import('cytoscape-navigator').NavigatorInstance
  }
}
