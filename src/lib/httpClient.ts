import axios, { AxiosInstance, AxiosRequestConfig } from 'axios'

const API_BASE_URL = 'http://172.16.11.187:8010/api/v1'

// 创建统一的HTTP客户端工具类
class HttpClient {
  private instance: AxiosInstance

  constructor(baseURL: string = API_BASE_URL) {
    this.instance = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 可以在这里添加认证 token
        // config.headers.Authorization = `Bearer ${token}`
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        return response.data
      },
      (error) => {
        console.error('HTTP Request Error:', error)
        return Promise.reject(error)
      }
    )
  }

  // GET 请求
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.get(url, config)
  }

  // POST 请求
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.post(url, data, config)
  }

  // PUT 请求
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.put(url, data, config)
  }

  // DELETE 请求
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.delete(url, config)
  }

  // PATCH 请求
  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.patch(url, data, config)
  }

  // 获取原始axios实例（用于特殊需求）
  getAxiosInstance(): AxiosInstance {
    return this.instance
  }
}

// 创建默认的HTTP客户端实例
export const httpClient = new HttpClient()

// 创建不带前缀的HTTP客户端（用于系统健康检查等）
export const httpClientRaw = new HttpClient('http://172.16.11.187:8010')

// 导出类以便创建自定义实例
export { HttpClient }

export default httpClient 