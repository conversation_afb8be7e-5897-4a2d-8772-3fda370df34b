// 颜色工具函数
// 提供颜色调试、预览和管理功能

import { colorManager, getLabelGroup } from './colorManager'

// 在控制台中显示当前颜色映射
export function showColorMapping(): void {
  const colors = colorManager.getAllColors()
  console.group('🎨 当前节点颜色映射')
  Object.entries(colors).forEach(([label, color]) => {
    console.log(`%c${label}: ${color}`, `color: ${color}; font-weight: bold; font-size: 14px;`)
  })
  console.groupEnd()

  // 分析颜色分布
  analyzeColorDistribution(colors)
}

// 分析颜色分布，检测重复或相似颜色
function analyzeColorDistribution(colors: Record<string, string>): void {
  const colorValues = Object.values(colors)
  const uniqueColors = new Set(colorValues)

  console.group('📊 颜色分布分析')
  console.log(`总标签数: ${colorValues.length}`)
  console.log(`唯一颜色数: ${uniqueColors.size}`)

  if (uniqueColors.size < colorValues.length) {
    console.warn(`⚠️ 发现 ${colorValues.length - uniqueColors.size} 个重复颜色`)

    // 找出重复的颜色
    const colorCount = new Map<string, string[]>()
    Object.entries(colors).forEach(([label, color]) => {
      if (!colorCount.has(color)) {
        colorCount.set(color, [])
      }
      colorCount.get(color)!.push(label)
    })

    colorCount.forEach((labels, color) => {
      if (labels.length > 1) {
        console.warn(`🔴 颜色 ${color} 被以下标签共享:`, labels)
      }
    })
  } else {
    console.log('✅ 所有颜色都是唯一的')
  }
  console.groupEnd()
}

// 生成颜色预览HTML（用于调试）
export function generateColorPreviewHTML(): string {
  const colors = colorManager.getAllColors()
  const html = Object.entries(colors).map(([label, color]) => 
    `<div style="display: inline-block; margin: 5px; padding: 10px; background: ${color}; color: white; border-radius: 5px; font-size: 12px;">${label}</div>`
  ).join('')
  
  return `<div style="font-family: Arial; padding: 20px;">
    <h3>节点标签颜色映射预览</h3>
    ${html}
  </div>`
}

// 批量设置颜色
export function setColors(colorMap: Record<string, string>): void {
  Object.entries(colorMap).forEach(([label, color]) => {
    colorManager.setColor(label, color)
  })
  console.log('✅ 颜色映射已更新')
  showColorMapping()
}

// 重置到默认颜色
export function resetColors(): void {
  colorManager.reset()
  console.log('🔄 颜色映射已重置')
}

// 验证颜色格式
export function isValidColor(color: string): boolean {
  // 创建临时元素测试颜色是否有效
  const testElement = document.createElement('div')
  testElement.style.color = color
  return testElement.style.color !== ''
}

// 获取标签的颜色（带验证）
export function getLabelColor(label: string): string {
  const color = colorManager.getColor(label)
  if (!isValidColor(color)) {
    console.warn(`⚠️ 标签 "${label}" 的颜色 "${color}" 无效`)
    return '#8993A4' // 备用颜色
  }
  return color
}

// 颜色对比度计算（确保文字可读性）
export function getContrastColor(backgroundColor: string): string {
  // 简单的对比度计算，返回黑色或白色
  const hex = backgroundColor.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  
  // 计算亮度
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  
  return brightness > 128 ? '#000000' : '#ffffff'
}

// 在浏览器控制台中可用的全局调试工具
if (typeof window !== 'undefined') {
  (window as any).colorDebug = {
    show: showColorMapping,
    preview: () => {
      const html = generateColorPreviewHTML()
      const newWindow = window.open()
      if (newWindow) {
        newWindow.document.write(html)
        newWindow.document.close()
      }
    },
    set: setColors,
    reset: resetColors,
    get: (label: string) => colorManager.getColor(label),
    getAll: () => colorManager.getAllColors(),
    stats: () => colorManager.getColorStats(),
    group: (label: string) => getLabelGroup(label)
  }
  
  console.log('🎨 完全动态颜色调试工具已载入! 使用 colorDebug.show() 查看当前映射')
  console.log('📊 新增功能: colorDebug.stats() 查看统计, colorDebug.group(label) 查看标签分组')
} 