// API 相关的工具函数

/**
 * 处理多个节点标签的格式化
 * @param nodeLabels 节点标签字符串
 * @returns 格式化后的标签字符串
 */
export const formatNodeLabels = (nodeLabels?: string): string => {
  if (!nodeLabels) return ''
  
  // 如果已经包含冒号，说明是多个标签连接，直接使用
  // 否则作为单个标签处理
  return `:${nodeLabels}`
}

/**
 * 增强Cypher查询，自动添加labels信息
 * @param query 原始查询语句
 * @returns 增强后的查询语句
 */
export const enhanceCypherQuery = (query: string): string => {
  if (!query.includes('labels(') && query.toUpperCase().includes('RETURN')) {
    // 简单的查询增强：如果返回节点，自动添加labels
    return query.replace(
      /RETURN\s+([^,\s]+(?:\s*,\s*[^,\s]+)*)/i,
      (match, returnClause) => {
        const nodeVars = returnClause.match(/\b[a-z]\b/g) || []
        const labelParts = nodeVars.map((v: string) => `labels(${v}) as ${v}_labels`)
        return labelParts.length > 0 
          ? `${match}, ${labelParts.join(', ')}`
          : match
      }
    )
  }
  return query
}

/**
 * 构建关系模式字符串
 * @param direction 关系方向
 * @param depth 关系深度
 * @param relationVar 关系变量名，默认为'r'
 * @returns 关系模式字符串
 */
export const buildRelationPattern = (
  direction: 'in' | 'out' | 'both' = 'both', 
  depth: number = 1,
  relationVar: string = 'r'
): string => {
  const depthPattern = `*1..${depth}`
  
  switch (direction) {
    case 'in':
      return `<-[${relationVar}${depthPattern}]-`
    case 'out':
      return `-[${relationVar}${depthPattern}]->`
    default:
      return `-[${relationVar}${depthPattern}]-`
  }
}

/**
 * 构建属性设置子句
 * @param properties 属性对象
 * @param nodeVar 节点变量名，默认为'n'
 * @returns SET子句字符串
 */
export const buildSetClause = (
  properties: Record<string, any>, 
  nodeVar: string = 'n'
): string => {
  return Object.entries(properties)
    .map(([key, value]) => `${nodeVar}.${key} = '${value}'`)
    .join(', ')
}

/**
 * 构建属性对象的Cypher字符串表示
 * @param properties 属性对象
 * @returns Cypher属性字符串
 */
export const buildPropertiesString = (properties: Record<string, any>): string => {
  if (!properties || Object.keys(properties).length === 0) {
    return '{}'
  }
  return JSON.stringify(properties).replace(/"/g, "'")
}

/**
 * 构建关系类型过滤器
 * @param relationshipTypes 关系类型数组
 * @returns 关系类型过滤字符串
 */
export const buildRelationshipTypeFilter = (relationshipTypes?: string[]): string => {
  if (!relationshipTypes || relationshipTypes.length === 0) {
    return ''
  }
  return `[${relationshipTypes.map(t => `:${t}`).join('|')}]`
}

/**
 * 处理API错误的通用函数
 * @param error 错误对象
 * @param context 错误上下文
 */
export const handleApiError = (error: any, context: string = 'API请求') => {
  console.error(`${context}失败:`, error)
  
  // 可以在这里添加错误上报、用户提示等逻辑
  if (error.response) {
    // 服务器响应错误
    console.error('响应状态:', error.response.status)
    console.error('响应数据:', error.response.data)
  } else if (error.request) {
    // 请求发送失败
    console.error('请求发送失败:', error.request)
  } else {
    // 其他错误
    console.error('错误信息:', error.message)
  }
  
  return error
}

/**
 * 创建带有默认参数的API调用函数
 * @param defaultParams 默认参数
 * @returns 参数合并函数
 */
export const withDefaultParams = <T extends Record<string, any>>(defaultParams: T) => {
  return (params?: Partial<T>): T => {
    return { ...defaultParams, ...params }
  }
}

/**
 * 延迟执行函数（用于防抖等场景）
 * @param fn 要执行的函数
 * @param delay 延迟时间（毫秒）
 * @returns Promise
 */
export const delay = (fn: () => void, delay: number): Promise<void> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      fn()
      resolve()
    }, delay)
  })
} 