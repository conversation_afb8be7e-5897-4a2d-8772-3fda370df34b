// 全局节点颜色管理器
// 完全基于后端schema动态分配颜色，支持图谱节点和边关系的动态变化

// 改进的动态颜色生成算法 - 确保大量标签时的颜色区分度
const generateDynamicColorPalette = (labels: string[]): Record<string, string> => {
  const colorMap: Record<string, string> = {}
  const labelCount = labels.length

  // 预定义的高对比度色相值（避免相近颜色）
  const baseHues = [0, 30, 60, 120, 180, 210, 240, 270, 300, 330]

  labels.forEach((label, index) => {
    let hue: number
    let saturation: number
    let lightness: number

    if (labelCount <= 10) {
      // 少量标签：使用预定义的高对比度色相
      hue = baseHues[index % baseHues.length]
      saturation = 70 + (index % 3) * 10 // 70-90%
      lightness = 50 + (index % 2) * 15  // 50% 或 65%
    } else if (labelCount <= 30) {
      // 中等数量标签：扩展色相范围，增加饱和度和明度变化
      const hueStep = 360 / Math.min(labelCount, 24) // 最多24个基础色相
      hue = (index * hueStep) % 360
      saturation = 60 + (index % 5) * 8  // 60-92%
      lightness = 45 + (index % 4) * 12  // 45-81%
    } else {
      // 大量标签：使用三维颜色空间分布
      const hueGroups = Math.ceil(labelCount / 6) // 每6个颜色一组
      const hueStep = 360 / Math.min(hueGroups, 30) // 最多30个色相组
      const groupIndex = Math.floor(index / 6)
      const inGroupIndex = index % 6

      hue = (groupIndex * hueStep) % 360

      // 在每个色相组内，通过饱和度和明度创建变化
      const saturationLevels = [55, 70, 85] // 3个饱和度级别
      const lightnessLevels = [40, 55] // 2个明度级别

      saturation = saturationLevels[inGroupIndex % saturationLevels.length]
      lightness = lightnessLevels[Math.floor(inGroupIndex / saturationLevels.length) % lightnessLevels.length]

      // 为了进一步区分，添加微调
      const microAdjustment = (inGroupIndex % 6) * 3
      saturation = Math.min(95, saturation + microAdjustment)
      lightness = Math.max(35, Math.min(75, lightness + (inGroupIndex % 2) * 10))
    }

    colorMap[label] = `hsl(${Math.round(hue)}, ${Math.round(saturation)}%, ${Math.round(lightness)}%)`
  })

  return colorMap
}

// 完全动态的标签相似度分析 - 无硬编码分类
const analyzeLabelSimilarity = (labels: string[]): Record<string, string[]> => {
  const groups: Record<string, string[]> = {}
  
  // 使用简单的字符串相似度将相似标签分组
  labels.forEach(label => {
    let assigned = false
    const lowerLabel = label.toLowerCase()
    
    // 查找是否有相似的现有组
    for (const [groupKey, groupLabels] of Object.entries(groups)) {
      const groupKeyLower = groupKey.toLowerCase()
      
      // 检查是否有共同的关键词或词根
      const hasCommonKeyword = hasCommonSubstring(lowerLabel, groupKeyLower, 3)
      
      if (hasCommonKeyword) {
        groupLabels.push(label)
        assigned = true
        break
      }
    }
    
    // 如果没有找到相似组，创建新组
    if (!assigned) {
      groups[label] = [label]
    }
  })
  
  return groups
}

// 检查两个字符串是否有长度不少于minLength的公共子串
const hasCommonSubstring = (str1: string, str2: string, minLength: number = 3): boolean => {
  for (let i = 0; i <= str1.length - minLength; i++) {
    const substring = str1.substring(i, i + minLength)
    if (str2.includes(substring)) {
      return true
    }
  }
  return false
}

// 全局颜色映射存储
let globalColorMap: Record<string, string> = {}

// 颜色管理器类 - 完全基于schema动态分配颜色，无任何硬编码
export class NodeColorManager {
  private static instance: NodeColorManager
  private colorMap: Record<string, string> = {}
  private labelGroups: Record<string, string[]> = {}

  private constructor() {
    // 空构造函数，无硬编码初始化
  }

  // 单例模式
  public static getInstance(): NodeColorManager {
    if (!NodeColorManager.instance) {
      NodeColorManager.instance = new NodeColorManager()
    }
    return NodeColorManager.instance
  }

  // 从schema数据完全动态初始化所有颜色
  public initializeFromSchema(nodeLabels: string[]): Record<string, string> {
    // 重置状态
    this.colorMap = {}
    this.labelGroups = {}

    // 使用完全动态的颜色生成
    this.colorMap = generateDynamicColorPalette(nodeLabels)

    // 分析标签相似度并分组
    this.labelGroups = analyzeLabelSimilarity(nodeLabels)

    // 更新全局颜色映射
    globalColorMap = { ...this.colorMap }

    return this.colorMap
  }

  // 为新标签动态分配颜色（用于运行时新增的标签）
  private assignColor(label: string): string {
    const existingLabels = Object.keys(this.colorMap)
    const allLabels = [...existingLabels, label]

    // 重新生成包含新标签的颜色映射
    const newColorMap = generateDynamicColorPalette(allLabels)

    // 保留现有标签的颜色，只获取新标签的颜色
    const newColor = newColorMap[label]
    this.colorMap[label] = newColor
    globalColorMap[label] = newColor

    return newColor
  }

  // 获取指定标签的颜色
  public getColor(label: string): string {
    if (!this.colorMap[label]) {
      // 动态分配新颜色
      return this.assignColor(label)
    }
    return this.colorMap[label]
  }

  // 获取所有颜色映射
  public getAllColors(): Record<string, string> {
    return { ...this.colorMap }
  }

  // 添加或更新颜色映射
  public setColor(label: string, color: string): void {
    this.colorMap[label] = color
    globalColorMap[label] = color
  }

  // 批量更新颜色映射
  public updateColors(schemaData: { node_labels?: string[], relationship_types?: string[] }): void {
    if (schemaData.node_labels) {
      // 保留现有颜色，只为新标签分配颜色
      const existingLabels = Object.keys(this.colorMap)
      const newLabels = schemaData.node_labels.filter(label => !existingLabels.includes(label))

      if (newLabels.length > 0) {
        // 为新标签分配颜色
        newLabels.forEach(label => {
          this.assignColor(label)
        })

        // 更新全局映射
        globalColorMap = { ...this.colorMap }
      }
    }
  }

  // 重置颜色管理器
  public reset(): void {
    this.colorMap = {}
    this.labelGroups = {}
    globalColorMap = {}
  }

  // 获取颜色统计信息
  public getColorStats(): { totalColors: number, labelGroups: Record<string, string[]> } {
    return {
      totalColors: Object.keys(this.colorMap).length,
      labelGroups: { ...this.labelGroups }
    }
  }
}

// 导出便捷函数
export const colorManager = NodeColorManager.getInstance()

// 兼容性函数 - 保持与现有代码的兼容
export function generateNodeColors(nodeTypes: string[]): Record<string, string> {
  return colorManager.initializeFromSchema(nodeTypes)
}

// 获取全局颜色映射
export function getGlobalColorMap(): Record<string, string> {
  return globalColorMap
}

// 获取单个标签颜色
export function getNodeColor(label: string): string {
  return colorManager.getColor(label)
}

// 更新schema颜色映射（用于动态添加新标签）
export function updateSchemaColors(schemaData: { node_labels?: string[], relationship_types?: string[] }): void {
  colorManager.updateColors(schemaData)
}

// 获取标签相似度分组信息
export function getLabelGroup(label: string): string[] {
  const groups = colorManager.getColorStats().labelGroups
  for (const [, groupLabels] of Object.entries(groups)) {
    if (groupLabels.includes(label)) {
      return groupLabels
    }
  }
  return [label] // 如果未找到分组，返回单独的标签
}

// 获取颜色统计
export function getColorStats() {
  return colorManager.getColorStats()
} 