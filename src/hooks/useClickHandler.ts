import { useRef, useCallback } from 'react'
import { useLatest } from 'ahooks'

interface UseClickHandlerOptions {
  onClick?: () => void
  onDoubleClick?: () => void
  delay?: number // 单击延迟时间，默认250ms
}

/**
 * 处理单击和双击事件冲突的自定义Hook
 * 使用 ahooks 的 useLatest 确保回调函数是最新的
 * 
 * @param options 配置选项
 * @returns 返回处理函数
 * 
 * @example
 * ```tsx
 * const { onClick, onDoubleClick } = useClickHandler({
 *   onClick: () => console.log('单击'),
 *   onDoubleClick: () => console.log('双击'),
 *   delay: 250
 * })
 * 
 * return <button onClick={onClick} onDoubleClick={onDoubleClick}>点击我</button>
 * ```
 */
export function useClickHandler(options: UseClickHandlerOptions) {
  const { onClick, onDoubleClick, delay = 250 } = options
  const clickTimerRef = useRef<number | null>(null)
  
  // 使用 ahooks 的 useLatest 确保回调函数始终是最新的
  const latestOnClick = useLatest(onClick)
  const latestOnDoubleClick = useLatest(onDoubleClick)

  const handleClick = useCallback(() => {
    // 如果有双击处理器，需要延迟单击处理
    if (latestOnDoubleClick.current) {
      // 清除之前的单击定时器
      if (clickTimerRef.current) {
        clearTimeout(clickTimerRef.current)
        clickTimerRef.current = null
      }

      // 延迟执行单击处理
      clickTimerRef.current = window.setTimeout(() => {
        latestOnClick.current?.()
        clickTimerRef.current = null
      }, delay)
    } else {
      // 如果没有双击处理器，直接执行单击
      latestOnClick.current?.()
    }
  }, [latestOnClick, latestOnDoubleClick, delay])

  const handleDoubleClick = useCallback(() => {
    // 清除单击定时器，防止单击事件触发
    if (clickTimerRef.current) {
      clearTimeout(clickTimerRef.current)
      clickTimerRef.current = null
    }

    // 执行双击处理
    latestOnDoubleClick.current?.()
  }, [latestOnDoubleClick])

  // 清理函数
  const cleanup = useCallback(() => {
    if (clickTimerRef.current) {
      clearTimeout(clickTimerRef.current)
      clickTimerRef.current = null
    }
  }, [])

  return {
    onClick: handleClick,
    onDoubleClick: latestOnDoubleClick.current ? handleDoubleClick : undefined,
    cleanup
  }
}

/**
 * 专门为Cytoscape节点事件设计的Hook
 * 使用 ahooks 的 useLatest 确保回调函数是最新的
 * 
 * @param onNodeSelect 节点选择回调
 * @param onNodeDoubleClick 节点双击回调
 * @param delay 延迟时间
 * @returns 返回事件处理器
 */
export function useCytoscapeNodeHandler(
  onNodeSelect?: (nodeData: any) => void,
  onNodeDoubleClick?: (nodeData: any) => void,
  delay = 250
) {
  const clickTimerRef = useRef<number | null>(null)
  
  // 使用 ahooks 的 useLatest 确保回调函数始终是最新的
  const latestOnNodeSelect = useLatest(onNodeSelect)
  const latestOnNodeDoubleClick = useLatest(onNodeDoubleClick)

  const handleNodeTap = useCallback((evt: any) => {
    const nodeData = evt.target.data()

    if (latestOnNodeDoubleClick.current) {
      // 清除之前的单击定时器
      if (clickTimerRef.current) {
        clearTimeout(clickTimerRef.current)
        clickTimerRef.current = null
      }

      // 延迟执行单击处理
      clickTimerRef.current = window.setTimeout(() => {
        latestOnNodeSelect.current?.(nodeData)
        clickTimerRef.current = null
      }, delay)
    } else {
      // 如果没有双击处理器，直接执行单击
      latestOnNodeSelect.current?.(nodeData)
    }
  }, [latestOnNodeSelect, latestOnNodeDoubleClick, delay])

  const handleNodeDoubleClick = useCallback((evt: any) => {
    // 清除单击定时器，防止单击事件触发
    if (clickTimerRef.current) {
      clearTimeout(clickTimerRef.current)
      clickTimerRef.current = null
    }

    const nodeData = evt.target.data()
    latestOnNodeDoubleClick.current?.(nodeData)
  }, [latestOnNodeDoubleClick])

  const cleanup = useCallback(() => {
    if (clickTimerRef.current) {
      clearTimeout(clickTimerRef.current)
      clickTimerRef.current = null
    }
  }, [])

  return {
    handleNodeTap,
    handleNodeDoubleClick: latestOnNodeDoubleClick.current ? handleNodeDoubleClick : undefined,
    cleanup
  }
}

/**
 * 使用第三方库 use-double-click 的包装器
 * 需要先安装: npm install use-double-click
 * 
 * @param onSingleClick 单击回调
 * @param onDoubleClick 双击回调
 * @param latency 延迟时间
 * @returns ref 和清理函数
 */
export function useThirdPartyDoubleClick(
  onSingleClick?: () => void,
  onDoubleClick?: () => void,
  latency = 250
) {
  // 这里可以在将来集成第三方库
  // 目前返回我们的实现
  return useClickHandler({
    onClick: onSingleClick,
    onDoubleClick: onDoubleClick,
    delay: latency
  })
} 