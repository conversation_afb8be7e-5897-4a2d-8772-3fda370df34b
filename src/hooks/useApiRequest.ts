import { useRequest, useDebounce, useMemoizedFn } from 'ahooks'
import { httpClient } from '@/lib/httpClient'
import { handleApiError } from '@/lib/apiUtils'

// 通用API请求hook
export const useApiRequest = <T = any>(
  service: () => Promise<T>,
  options?: {
    manual?: boolean
    onSuccess?: (data: T) => void
    onError?: (error: any) => void
    debounceWait?: number
  }
) => {
  const { manual = false, debounceWait = 0, onSuccess, onError } = options || {}

  // 错误处理函数
  const handleError = useMemoizedFn((error: any) => {
    handleApiError(error, '请求')
    onError?.(error)
  })

  // 成功处理函数
  const handleSuccess = useMemoizedFn((data: T) => {
    onSuccess?.(data)
  })

  const result = useRequest(service, {
    manual,
    onSuccess: handleSuccess,
    onError: handleError
  })

  // 如果需要防抖，使用防抖版本的run函数
  const debouncedRun = useDebounce(result.run, { wait: debounceWait })

  return {
    ...result,
    run: debounceWait > 0 ? debouncedRun : result.run
  }
}

// 搜索API hook（带防抖）
export const useSearchApi = <T = any>(
  _searchFn: (keyword: string) => Promise<T>,
  options?: {
    debounceWait?: number
    onSuccess?: (data: T) => void
    onError?: (error: any) => void
  }
) => {
  const { debounceWait = 300, ...restOptions } = options || {}

  return useApiRequest(
    () => Promise.resolve({} as T), // 占位服务
    {
      manual: true,
      debounceWait,
      ...restOptions
    }
  )
}

// 分页API hook
export const usePaginationApi = <T = any>(
  service: (params: { current: number; pageSize: number; [key: string]: any }) => Promise<{
    data: T[]
    total: number
  }>,
  options?: {
    defaultPageSize?: number
    onSuccess?: (data: { data: T[]; total: number }) => void
    onError?: (error: any) => void
  }
) => {
  const { defaultPageSize = 20, ...restOptions } = options || {}

  return useRequest(service, {
    defaultParams: [{ current: 1, pageSize: defaultPageSize }],
    ...restOptions
  })
}

// 节点详情API hook
export const useNodeDetails = (nodeId?: string | number, nodeLabels?: string) => {
  return useRequest(
    async () => {
      if (!nodeId) throw new Error('节点ID不能为空')
      
      return httpClient.post('/neo4j/query', {
        query: `MATCH (n${nodeLabels ? `:${nodeLabels}` : ''} {business_id: '${nodeId}'}) RETURN n, labels(n) as n_labels`,
        response_format: 'reference'
      })
    },
    {
      manual: !nodeId,
      refreshDeps: [nodeId, nodeLabels]
    }
  )
}

// 节点邻居API hook
export const useNodeNeighbors = (
  nodeId?: string | number, 
  nodeLabels?: string,
  options?: {
    depth?: number
    direction?: 'in' | 'out' | 'both'
    limit?: number
  }
) => {
  const { depth = 1, direction = 'both', limit = 20 } = options || {}

  return useRequest(
    async () => {
      if (!nodeId) throw new Error('节点ID不能为空')
      
      const label = nodeLabels ? `:${nodeLabels}` : ''
      let relationPattern = ''
      
      switch (direction) {
        case 'in':
          relationPattern = `<-[r*1..${depth}]-`
          break
        case 'out':
          relationPattern = `-[r*1..${depth}]->`
          break
        default:
          relationPattern = `-[r*1..${depth}]-`
      }

      return httpClient.post('/neo4j/query', {
        query: `MATCH (n${label} {business_id: '${nodeId}'})${relationPattern}(m) RETURN n, r, m, labels(n) as n_labels, labels(m) as m_labels LIMIT ${limit}`,
        response_format: 'reference'
      })
    },
    {
      manual: !nodeId,
      refreshDeps: [nodeId, nodeLabels, depth, direction, limit]
    }
  )
}

// Cypher查询hook
export const useCypherQuery = () => {
  return useRequest(
    async (query: string, parameters?: Record<string, any>) => {
      return httpClient.post('/neo4j/query', {
        query,
        parameters,
        response_format: 'reference'
      })
    },
    {
      manual: true
    }
  )
} 