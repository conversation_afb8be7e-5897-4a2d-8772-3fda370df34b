{"openapi": "3.1.0", "info": {"title": "BZN 关系图谱系统API", "description": "重构后的关系图谱REST API服务", "version": "2.0.0"}, "paths": {"/": {"get": {"summary": "Root", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"summary": "Basic Health", "operationId": "basic_health_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/nodes/areas/": {"get": {"tags": ["区域节点管理"], "summary": "搜索区域节点", "description": "搜索区域节点", "operationId": "search_areas_api_v1_nodes_areas__get", "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "区域名称关键词", "title": "Name"}, "description": "区域名称关键词"}, {"name": "code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "区域编码关键词", "title": "Code"}, "description": "区域编码关键词"}, {"name": "level", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "maximum": 10, "minimum": 0}, {"type": "null"}], "description": "区域层级", "title": "Level"}, "description": "区域层级"}, {"name": "parent_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "父级区域编码", "title": "Parent Code"}, "description": "父级区域编码"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "返回记录数限制", "default": 100, "title": "Limit"}, "description": "返回记录数限制"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Search Areas Api V1 Nodes Areas  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["区域节点管理"], "summary": "删除所有区域数据", "description": "删除所有区域数据", "operationId": "delete_all_areas_api_v1_nodes_areas__delete", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Delete All Areas Api V1 Nodes Areas  Delete"}}}}}}}, "/api/v1/nodes/areas/statistics": {"get": {"tags": ["区域节点管理"], "summary": "获取区域统计信息", "description": "获取区域节点统计信息", "operationId": "get_statistics_api_v1_nodes_areas_statistics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Statistics Api V1 Nodes Areas Statistics Get"}}}}}}}, "/api/v1/nodes/areas/health": {"get": {"tags": ["区域节点管理"], "summary": "健康检查", "description": "区域服务健康检查", "operationId": "health_check_api_v1_nodes_areas_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Health Check Api V1 Nodes Areas Health Get"}}}}}}}, "/api/v1/nodes/areas/{code}": {"get": {"tags": ["区域节点管理"], "summary": "根据编码获取区域", "description": "根据编码获取单个区域", "operationId": "get_area_by_code_api_v1_nodes_areas__code__get", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "description": "区域编码", "title": "Code"}, "description": "区域编码"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Get Area By Code Api V1 Nodes Areas  Code  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/nodes/areas/import": {"post": {"tags": ["区域节点管理"], "summary": "导入区域数据", "description": "从数据源导入区域数据", "operationId": "import_areas_api_v1_nodes_areas_import_post", "parameters": [{"name": "clear_existing", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否清空现有数据", "default": false, "title": "Clear Existing"}, "description": "是否清空现有数据"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Import Areas Api V1 Nodes Areas Import Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/nodes/persons/{person_type}/": {"get": {"tags": ["人员节点管理"], "summary": "搜索指定类型的人员", "description": "搜索指定类型的人员\n\nArgs:\n    person_type: 人员类型 (agents/bd/mo/insured)\n    name: 姓名关键词\n    code: 人员编码关键词\n    phone: 手机号关键词\n    city: 城市关键词\n    status: 状态\n    limit: 返回记录数限制\n    \nReturns:\n    Dict[str, Any]: 包含人员列表和元数据的响应", "operationId": "search_persons_api_v1_nodes_persons__person_type___get", "parameters": [{"name": "person_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/PersonType", "description": "人员类型"}, "description": "人员类型"}, {"name": "name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "姓名关键词", "title": "Name"}, "description": "姓名关键词"}, {"name": "code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "人员编码关键词", "title": "Code"}, "description": "人员编码关键词"}, {"name": "phone", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "手机号关键词", "title": "Phone"}, "description": "手机号关键词"}, {"name": "city", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "城市关键词", "title": "City"}, "description": "城市关键词"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "状态", "title": "Status"}, "description": "状态"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "返回记录数限制", "default": 100, "title": "Limit"}, "description": "返回记录数限制"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Search Persons Api V1 Nodes Persons  Person Type   Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["人员节点管理"], "summary": "删除所有指定类型的人员数据", "description": "删除所有指定类型的人员数据\n\nArgs:\n    person_type: 人员类型\n    \nReturns:\n    Dict[str, Any]: 删除结果", "operationId": "delete_all_persons_api_v1_nodes_persons__person_type___delete", "parameters": [{"name": "person_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/PersonType", "description": "人员类型"}, "description": "人员类型"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Delete All Persons Api V1 Nodes Persons  Person Type   Delete"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/nodes/persons/{person_type}/statistics": {"get": {"tags": ["人员节点管理"], "summary": "获取人员统计信息", "description": "获取指定类型人员的统计信息\n\nArgs:\n    person_type: 人员类型\n    \nReturns:\n    Dict[str, Any]: 统计信息", "operationId": "get_person_statistics_api_v1_nodes_persons__person_type__statistics_get", "parameters": [{"name": "person_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/PersonType", "description": "人员类型"}, "description": "人员类型"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Get Person Statistics Api V1 Nodes Persons  Person Type  Statistics Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/nodes/persons/{person_type}/{code}": {"get": {"tags": ["人员节点管理"], "summary": "根据编码获取人员", "description": "根据编码获取单个人员\n\nArgs:\n    person_type: 人员类型\n    code: 人员编码\n    \nReturns:\n    Dict[str, Any]: 人员详细信息", "operationId": "get_person_by_code_api_v1_nodes_persons__person_type___code__get", "parameters": [{"name": "person_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/PersonType", "description": "人员类型"}, "description": "人员类型"}, {"name": "code", "in": "path", "required": true, "schema": {"type": "string", "description": "人员编码", "title": "Code"}, "description": "人员编码"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Get Person By Code Api V1 Nodes Persons  Person Type   Code  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/nodes/persons/{person_type}/import": {"post": {"tags": ["人员节点管理"], "summary": "导入人员数据", "description": "从数据源导入指定类型的人员数据\n\nArgs:\n    person_type: 人员类型\n    clear_existing: 是否清空现有数据\n    \nReturns:\n    Dict[str, Any]: 导入结果", "operationId": "import_persons_api_v1_nodes_persons__person_type__import_post", "parameters": [{"name": "person_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/PersonType", "description": "人员类型"}, "description": "人员类型"}, {"name": "clear_existing", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否清空现有数据", "default": false, "title": "Clear Existing"}, "description": "是否清空现有数据"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Import Persons Api V1 Nodes Persons  Person Type  Import Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/nodes/persons/": {"get": {"tags": ["人员节点管理"], "summary": "获取所有人员类型的统计概览", "description": "获取所有人员类型的统计概览\n\nReturns:\n    Dict[str, Any]: 所有人员类型的统计信息", "operationId": "get_all_persons_overview_api_v1_nodes_persons__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get All Persons Overview Api V1 Nodes Persons  Get"}}}}}}}, "/api/v1/nodes/persons/health": {"get": {"tags": ["人员节点管理"], "summary": "人员服务健康检查", "description": "人员节点服务健康检查\n\nReturns:\n    Dict[str, Any]: 健康状态", "operationId": "health_check_api_v1_nodes_persons_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Health Check Api V1 Nodes Persons Health Get"}}}}}}}, "/api/v1/nodes/organizations/{org_type}/": {"get": {"tags": ["组织节点管理"], "summary": "搜索指定类型的组织", "description": "搜索指定类型的组织\n\nArgs:\n    org_type: 组织类型\n    name: 组织名称关键词\n    code: 组织编码关键词\n    province: 省份\n    city: 城市\n    limit: 返回记录数限制\n    \nReturns:\n    Dict[str, Any]: 包含组织列表和元数据的响应", "operationId": "search_organizations_api_v1_nodes_organizations__org_type___get", "parameters": [{"name": "org_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/OrganizationType", "description": "组织类型"}, "description": "组织类型"}, {"name": "name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "组织名称关键词", "title": "Name"}, "description": "组织名称关键词"}, {"name": "code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "组织编码关键词", "title": "Code"}, "description": "组织编码关键词"}, {"name": "province", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "省份", "title": "Province"}, "description": "省份"}, {"name": "city", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "城市", "title": "City"}, "description": "城市"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "返回记录数限制", "default": 100, "title": "Limit"}, "description": "返回记录数限制"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Search Organizations Api V1 Nodes Organizations  Org Type   Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["组织节点管理"], "summary": "删除所有指定类型的组织数据", "description": "删除所有指定类型的组织数据\n\nArgs:\n    org_type: 组织类型\n    \nReturns:\n    Dict[str, Any]: 删除结果", "operationId": "delete_all_organizations_api_v1_nodes_organizations__org_type___delete", "parameters": [{"name": "org_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/OrganizationType", "description": "组织类型"}, "description": "组织类型"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Delete All Organizations Api V1 Nodes Organizations  Org Type   Delete"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/nodes/organizations/{org_type}/statistics": {"get": {"tags": ["组织节点管理"], "summary": "获取组织统计信息", "description": "获取指定类型组织的统计信息\n\nArgs:\n    org_type: 组织类型\n    \nReturns:\n    Dict[str, Any]: 统计信息", "operationId": "get_organization_statistics_api_v1_nodes_organizations__org_type__statistics_get", "parameters": [{"name": "org_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/OrganizationType", "description": "组织类型"}, "description": "组织类型"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Get Organization Statistics Api V1 Nodes Organizations  Org Type  Statistics Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/nodes/organizations/{org_type}/import": {"post": {"tags": ["组织节点管理"], "summary": "导入组织数据", "description": "从数据源导入指定类型的组织数据\n\nArgs:\n    org_type: 组织类型\n    clear_existing: 是否清空现有数据\n    \nReturns:\n    Dict[str, Any]: 导入结果", "operationId": "import_organizations_api_v1_nodes_organizations__org_type__import_post", "parameters": [{"name": "org_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/OrganizationType", "description": "组织类型"}, "description": "组织类型"}, {"name": "clear_existing", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否清空现有数据", "default": false, "title": "Clear Existing"}, "description": "是否清空现有数据"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Import Organizations Api V1 Nodes Organizations  Org Type  Import Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/nodes/organizations/{org_type}/{code}": {"get": {"tags": ["组织节点管理"], "summary": "根据编码获取组织", "description": "根据编码获取单个组织\n\nArgs:\n    org_type: 组织类型\n    code: 组织编码\n    \nReturns:\n    Dict[str, Any]: 组织详细信息", "operationId": "get_organization_by_code_api_v1_nodes_organizations__org_type___code__get", "parameters": [{"name": "org_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/OrganizationType", "description": "组织类型"}, "description": "组织类型"}, {"name": "code", "in": "path", "required": true, "schema": {"type": "string", "description": "组织编码", "title": "Code"}, "description": "组织编码"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Get Organization By Code Api V1 Nodes Organizations  Org Type   Code  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/nodes/organizations/": {"get": {"tags": ["组织节点管理"], "summary": "获取所有组织类型的统计概览", "description": "获取所有组织类型的统计概览\n\nReturns:\n    Dict[str, Any]: 所有组织类型的统计信息", "operationId": "get_all_organizations_overview_api_v1_nodes_organizations__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get All Organizations Overview Api V1 Nodes Organizations  Get"}}}}}}}, "/api/v1/nodes/organizations/health": {"get": {"tags": ["组织节点管理"], "summary": "组织服务健康检查", "description": "组织节点服务健康检查\n\nReturns:\n    Dict[str, Any]: 健康状态", "operationId": "health_check_api_v1_nodes_organizations_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Health Check Api V1 Nodes Organizations Health Get"}}}}}}}, "/api/v1/nodes/policies/": {"get": {"tags": ["保单节点管理"], "summary": "搜索保单节点", "description": "搜索保单节点", "operationId": "search_policies_api_v1_nodes_policies__get", "parameters": [{"name": "policy_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "保单编码关键词", "title": "Policy Code"}, "description": "保单编码关键词"}, {"name": "insured_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "被保险人姓名关键词", "title": "Insured Name"}, "description": "被保险人姓名关键词"}, {"name": "insurance_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "保险公司编码", "title": "Insurance Code"}, "description": "保险公司编码"}, {"name": "product_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "产品编码", "title": "Product Code"}, "description": "产品编码"}, {"name": "policy_status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "保单状态", "title": "Policy Status"}, "description": "保单状态"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "起保日期(YYYY-MM-DD)", "title": "Start Date"}, "description": "起保日期(YYYY-MM-DD)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "终保日期(YYYY-MM-DD)", "title": "End Date"}, "description": "终保日期(YYYY-MM-DD)"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "返回记录数限制", "default": 100, "title": "Limit"}, "description": "返回记录数限制"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Search Policies Api V1 Nodes Policies  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["保单节点管理"], "summary": "删除所有保单数据", "description": "删除所有保单数据", "operationId": "delete_all_policies_api_v1_nodes_policies__delete", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Delete All Policies Api V1 Nodes Policies  Delete"}}}}}}}, "/api/v1/nodes/policies/statistics": {"get": {"tags": ["保单节点管理"], "summary": "获取保单统计信息", "description": "获取保单节点统计信息", "operationId": "get_statistics_api_v1_nodes_policies_statistics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Statistics Api V1 Nodes Policies Statistics Get"}}}}}}}, "/api/v1/nodes/policies/health": {"get": {"tags": ["保单节点管理"], "summary": "健康检查", "description": "保单服务健康检查", "operationId": "health_check_api_v1_nodes_policies_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Health Check Api V1 Nodes Policies Health Get"}}}}}}}, "/api/v1/nodes/policies/import": {"post": {"tags": ["保单节点管理"], "summary": "导入保单数据", "description": "从数据源导入保单数据", "operationId": "import_policies_api_v1_nodes_policies_import_post", "parameters": [{"name": "clear_existing", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否清空现有数据", "default": false, "title": "Clear Existing"}, "description": "是否清空现有数据"}, {"name": "limit", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "导入记录数限制", "title": "Limit"}, "description": "导入记录数限制"}, {"name": "where_conditions", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "额外的WHERE条件", "title": "Where Conditions"}, "description": "额外的WHERE条件"}, {"name": "custom_day", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "自定义日期(YYYY-MM-DD)", "title": "Custom Day"}, "description": "自定义日期(YYYY-MM-DD)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Import Policies Api V1 Nodes Policies Import Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/nodes/policies/{policy_code}": {"get": {"tags": ["保单节点管理"], "summary": "根据编码获取保单", "description": "根据编码获取单个保单", "operationId": "get_policy_by_code_api_v1_nodes_policies__policy_code__get", "parameters": [{"name": "policy_code", "in": "path", "required": true, "schema": {"type": "string", "description": "保单编码", "title": "Policy Code"}, "description": "保单编码"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Get Policy By Code Api V1 Nodes Policies  Policy Code  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/nodes/products/": {"get": {"tags": ["产品节点管理"], "summary": "搜索产品节点", "description": "搜索产品节点\n\nReturns:\n    Dict[str, Any]: 包含产品列表和元数据的响应", "operationId": "search_products_api_v1_nodes_products__get", "parameters": [{"name": "product_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "产品编码关键词", "title": "Product Code"}, "description": "产品编码关键词"}, {"name": "product_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "产品名称关键词", "title": "Product Name"}, "description": "产品名称关键词"}, {"name": "insurance_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "保险公司编码", "title": "Insurance Code"}, "description": "保险公司编码"}, {"name": "product_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "产品类型", "title": "Product Type"}, "description": "产品类型"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "产品状态", "title": "Status"}, "description": "产品状态"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "返回记录数限制", "default": 100, "title": "Limit"}, "description": "返回记录数限制"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Search Products Api V1 Nodes Products  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["产品节点管理"], "summary": "删除所有产品数据", "description": "删除所有产品数据\n\nReturns:\n    Dict[str, Any]: 删除结果", "operationId": "delete_all_products_api_v1_nodes_products__delete", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Delete All Products Api V1 Nodes Products  Delete"}}}}}}}, "/api/v1/nodes/products/statistics": {"get": {"tags": ["产品节点管理"], "summary": "获取产品统计信息", "description": "获取产品节点统计信息\n\nReturns:\n    Dict[str, Any]: 统计信息", "operationId": "get_statistics_api_v1_nodes_products_statistics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Statistics Api V1 Nodes Products Statistics Get"}}}}}}}, "/api/v1/nodes/products/{product_code}": {"get": {"tags": ["产品节点管理"], "summary": "根据编码获取产品", "description": "根据编码获取单个产品\n\nArgs:\n    product_code: 产品编码\n    \nReturns:\n    Dict[str, Any]: 产品详细信息", "operationId": "get_product_by_code_api_v1_nodes_products__product_code__get", "parameters": [{"name": "product_code", "in": "path", "required": true, "schema": {"type": "string", "description": "产品编码", "title": "Product Code"}, "description": "产品编码"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Get Product By Code Api V1 Nodes Products  Product Code  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/nodes/products/import": {"post": {"tags": ["产品节点管理"], "summary": "导入产品数据", "description": "从数据源导入产品数据\n\nArgs:\n    clear_existing: 是否清空现有数据\n    limit: 导入记录数限制\n    where_conditions: 额外的WHERE条件\n    custom_day: 自定义日期\n    \nReturns:\n    Dict[str, Any]: 导入结果", "operationId": "import_products_api_v1_nodes_products_import_post", "parameters": [{"name": "clear_existing", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否清空现有数据", "default": false, "title": "Clear Existing"}, "description": "是否清空现有数据"}, {"name": "limit", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "导入记录数限制", "title": "Limit"}, "description": "导入记录数限制"}, {"name": "where_conditions", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "额外的WHERE条件", "title": "Where Conditions"}, "description": "额外的WHERE条件"}, {"name": "custom_day", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "自定义日期(YYYY-MM-DD)", "title": "Custom Day"}, "description": "自定义日期(YYYY-MM-DD)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Import Products Api V1 Nodes Products Import Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/nodes/products/health": {"get": {"tags": ["产品节点管理"], "summary": "健康检查", "description": "产品节点服务健康检查\n\nReturns:\n    Dict[str, Any]: 健康状态", "operationId": "health_check_api_v1_nodes_products_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Health Check Api V1 Nodes Products Health Get"}}}}}}}, "/api/v1/relationships/geographic/{relation_type}/": {"get": {"tags": ["地理位置关系管理"], "summary": "搜索地理位置关系", "description": "搜索指定类型的地理位置关系\n\nArgs:\n    relation_type: 地理位置关系类型\n    source_code: 源节点编码\n    target_code: 目标节点编码\n    area_name: 区域名称关键词\n    province: 省份\n    city: 城市\n    include_details: 是否包含节点详细信息\n    limit: 返回记录数限制\n    \nReturns:\n    Dict[str, Any]: 包含关系列表和元数据的响应", "operationId": "search_geographic_relationships_api_v1_relationships_geographic__relation_type___get", "parameters": [{"name": "relation_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/GeographicRelationType", "description": "地理位置关系类型"}, "description": "地理位置关系类型"}, {"name": "source_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "源节点编码", "title": "Source Code"}, "description": "源节点编码"}, {"name": "target_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "目标节点编码", "title": "Target Code"}, "description": "目标节点编码"}, {"name": "area_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "区域名称关键词", "title": "Area Name"}, "description": "区域名称关键词"}, {"name": "province", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "省份", "title": "Province"}, "description": "省份"}, {"name": "city", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "城市", "title": "City"}, "description": "城市"}, {"name": "include_details", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否包含节点详细信息", "default": true, "title": "Include Details"}, "description": "是否包含节点详细信息"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "返回记录数限制", "default": 100, "title": "Limit"}, "description": "返回记录数限制"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Search Geographic Relationships Api V1 Relationships Geographic  Relation Type   Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["地理位置关系管理"], "summary": "删除所有指定类型的地理位置关系", "description": "删除所有指定类型的地理位置关系\n\nArgs:\n    relation_type: 地理位置关系类型\n    \nReturns:\n    Dict[str, Any]: 删除结果", "operationId": "delete_all_geographic_relationships_api_v1_relationships_geographic__relation_type___delete", "parameters": [{"name": "relation_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/GeographicRelationType", "description": "地理位置关系类型"}, "description": "地理位置关系类型"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Delete All Geographic Relationships Api V1 Relationships Geographic  Relation Type   Delete"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/relationships/geographic/{relation_type}/statistics": {"get": {"tags": ["地理位置关系管理"], "summary": "获取地理位置关系统计信息", "description": "获取指定类型地理位置关系的统计信息\n\nArgs:\n    relation_type: 地理位置关系类型\n    \nReturns:\n    Dict[str, Any]: 统计信息", "operationId": "get_geographic_relationship_statistics_api_v1_relationships_geographic__relation_type__statistics_get", "parameters": [{"name": "relation_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/GeographicRelationType", "description": "地理位置关系类型"}, "description": "地理位置关系类型"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Get Geographic Relationship Statistics Api V1 Relationships Geographic  Relation Type  Statistics Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/relationships/geographic/{relation_type}/extract": {"post": {"tags": ["地理位置关系管理"], "summary": "从数据源提取地理位置关系", "description": "从数据源提取指定类型的地理位置关系\n\nArgs:\n    relation_type: 地理位置关系类型\n    clear_existing: 是否清空现有关系\n    limit: 提取记录数限制\n    \nReturns:\n    Dict[str, Any]: 提取结果", "operationId": "extract_geographic_relationships_api_v1_relationships_geographic__relation_type__extract_post", "parameters": [{"name": "relation_type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/GeographicRelationType", "description": "地理位置关系类型"}, "description": "地理位置关系类型"}, {"name": "clear_existing", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否清空现有关系", "default": false, "title": "Clear Existing"}, "description": "是否清空现有关系"}, {"name": "limit", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "提取记录数限制", "title": "Limit"}, "description": "提取记录数限制"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Extract Geographic Relationships Api V1 Relationships Geographic  Relation Type  Extract Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/relationships/geographic/": {"get": {"tags": ["地理位置关系管理"], "summary": "获取所有地理位置关系类型的统计概览", "description": "获取所有地理位置关系类型的统计概览\n\nReturns:\n    Dict[str, Any]: 所有地理位置关系类型的统计信息", "operationId": "get_all_geographic_relationships_overview_api_v1_relationships_geographic__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get All Geographic Relationships Overview Api V1 Relationships Geographic  Get"}}}}}}}, "/api/v1/relationships/geographic/health": {"get": {"tags": ["地理位置关系管理"], "summary": "地理位置关系服务健康检查", "description": "地理位置关系服务健康检查\n\nReturns:\n    Dict[str, Any]: 健康状态", "operationId": "health_check_api_v1_relationships_geographic_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Health Check Api V1 Relationships Geographic Health Get"}}}}}}}, "/api/v1/relationships/business/": {"get": {"tags": ["业务关系管理"], "summary": "搜索业务关系", "description": "搜索业务关系\n\nArgs:\n    relation_type: 关系类型\n    policy_code: 保单编码关键词\n    product_code: 产品编码关键词\n    person_id: 人员ID关键词\n    include_node_details: 是否包含节点详情\n    limit: 返回记录数限制\n    \nReturns:\n    Dict[str, Any]: 包含关系列表和元数据的响应", "operationId": "search_business_relationships_api_v1_relationships_business__get", "parameters": [{"name": "relation_type", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/BusinessRelationType", "description": "关系类型"}, "description": "关系类型"}, {"name": "policy_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "保单编码关键词", "title": "Policy Code"}, "description": "保单编码关键词"}, {"name": "product_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "产品编码关键词", "title": "Product Code"}, "description": "产品编码关键词"}, {"name": "person_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "人员ID关键词", "title": "Person Id"}, "description": "人员ID关键词"}, {"name": "include_node_details", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否包含节点详情", "default": true, "title": "Include Node Details"}, "description": "是否包含节点详情"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "返回记录数限制", "default": 100, "title": "Limit"}, "description": "返回记录数限制"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Search Business Relationships Api V1 Relationships Business  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["业务关系管理"], "summary": "删除业务关系数据", "description": "删除指定类型的业务关系数据\n\nArgs:\n    relation_type: 关系类型\n    \nReturns:\n    Dict[str, Any]: 删除结果", "operationId": "delete_business_relationships_api_v1_relationships_business__delete", "parameters": [{"name": "relation_type", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/BusinessRelationType", "description": "关系类型"}, "description": "关系类型"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Delete Business Relationships Api V1 Relationships Business  Delete"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/relationships/business/statistics": {"get": {"tags": ["业务关系管理"], "summary": "获取业务关系统计信息", "description": "获取业务关系统计信息\n\nArgs:\n    relation_type: 关系类型，不指定则返回所有类型的统计\n    \nReturns:\n    Dict[str, Any]: 统计信息", "operationId": "get_business_statistics_api_v1_relationships_business_statistics_get", "parameters": [{"name": "relation_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/BusinessRelationType"}, {"type": "null"}], "description": "关系类型", "title": "Relation Type"}, "description": "关系类型"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Get Business Statistics Api V1 Relationships Business Statistics Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/relationships/business/import": {"post": {"tags": ["业务关系管理"], "summary": "导入业务关系数据", "description": "从数据源导入业务关系数据\n\nArgs:\n    relation_type: 关系类型\n    clear_existing: 是否清空现有数据\n    limit: 导入记录数限制\n    where_conditions: 额外的WHERE条件\n    custom_day: 自定义日期\n    \nReturns:\n    Dict[str, Any]: 导入结果", "operationId": "import_business_relationships_api_v1_relationships_business_import_post", "parameters": [{"name": "relation_type", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/BusinessRelationType", "description": "关系类型"}, "description": "关系类型"}, {"name": "clear_existing", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否清空现有数据", "default": false, "title": "Clear Existing"}, "description": "是否清空现有数据"}, {"name": "limit", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "导入记录数限制", "title": "Limit"}, "description": "导入记录数限制"}, {"name": "where_conditions", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "额外的WHERE条件", "title": "Where Conditions"}, "description": "额外的WHERE条件"}, {"name": "custom_day", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "自定义日期(YYYY-MM-DD)", "title": "Custom Day"}, "description": "自定义日期(YYYY-MM-DD)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Import Business Relationships Api V1 Relationships Business Import Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/relationships/business/health": {"get": {"tags": ["业务关系管理"], "summary": "健康检查", "description": "业务关系服务健康检查\n\nReturns:\n    Dict[str, Any]: 健康状态", "operationId": "health_check_api_v1_relationships_business_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Health Check Api V1 Relationships Business Health Get"}}}}}}}, "/api/v1/system/health/": {"get": {"tags": ["系统健康检查"], "summary": "系统整体健康检查", "description": "系统整体健康检查\n\nReturns:\n    Dict[str, Any]: 系统健康状态", "operationId": "system_health_check_api_v1_system_health__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response System Health Check Api V1 System Health  Get"}}}}}}}, "/api/v1/system/health/database": {"get": {"tags": ["系统健康检查"], "summary": "数据库健康检查", "description": "数据库专项健康检查\n\nReturns:\n    Dict[str, Any]: 数据库健康状态", "operationId": "database_health_check_api_v1_system_health_database_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Database Health Check Api V1 System Health Database Get"}}}}}}}, "/api/v1/system/health/services": {"get": {"tags": ["系统健康检查"], "summary": "服务组件健康检查", "description": "服务组件健康检查\n\nReturns:\n    Dict[str, Any]: 服务组件健康状态", "operationId": "services_health_check_api_v1_system_health_services_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Services Health Check Api V1 System Health Services Get"}}}}}}}, "/api/v1/system/health/detailed": {"get": {"tags": ["系统健康检查"], "summary": "详细健康检查报告", "description": "生成详细的健康检查报告\n\nReturns:\n    Dict[str, Any]: 详细健康检查报告", "operationId": "detailed_health_check_api_v1_system_health_detailed_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Detailed Health Check Api V1 System Health Detailed Get"}}}}}}}, "/api/v1/neo4j/query": {"post": {"tags": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>"], "summary": "执行Cypher查询", "description": "执行Cypher查询", "operationId": "execute_cypher_query_api_v1_neo4j_query_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CypherQueryRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/neo4j/schema": {"get": {"tags": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>"], "summary": "获取数据库模式信息", "description": "获取数据库模式信息", "operationId": "get_database_schema_api_v1_neo4j_schema_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Database Schema Api V1 Neo4J Schema Get"}}}}}}}, "/api/v1/neo4j/statistics": {"get": {"tags": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>"], "summary": "获取数据库统计信息", "description": "获取数据库统计信息", "operationId": "get_database_statistics_api_v1_neo4j_statistics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Database Statistics Api V1 Neo4J Statistics Get"}}}}}}}, "/api/v1/neo4j/quick/sample": {"get": {"tags": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>"], "summary": "获取数据库样本", "description": "获取数据库样本", "operationId": "quick_sample_data_api_v1_neo4j_quick_sample_get", "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"BusinessRelationType": {"type": "string", "enum": ["policy_product", "insured_policy", "policy_salesperson"], "title": "BusinessRelationType", "description": "业务关系类型枚举"}, "CypherQueryRequest": {"properties": {"query": {"type": "string", "title": "Query", "description": "Cypher查询语句"}, "parameters": {"additionalProperties": true, "type": "object", "title": "Parameters", "description": "查询参数"}, "response_format": {"$ref": "#/components/schemas/ResponseFormat", "description": "返回格式", "default": "reference"}}, "type": "object", "required": ["query"], "title": "CypherQueryRequest"}, "GeographicRelationType": {"type": "string", "enum": ["area-hierarchy", "brokerage-company-area", "claims-area", "policy-company-area", "sales-channel-area"], "title": "GeographicRelationType", "description": "地理位置关系类型枚举"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "OrganizationType": {"type": "string", "enum": ["insurance-companies", "brokerage-companies", "sales-channels", "policy-companies"], "title": "OrganizationType", "description": "组织类型枚举"}, "PersonType": {"type": "string", "enum": ["agents", "bd", "mo", "insured"], "title": "PersonType", "description": "人员类型枚举"}, "ResponseFormat": {"type": "string", "enum": ["standard", "reference"], "title": "ResponseFormat"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}