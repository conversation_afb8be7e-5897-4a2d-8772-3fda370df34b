{"name": "bzn-relational-graph-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "./scripts/start-dev.sh", "dev:simple": "vite", "dev:clean": "./scripts/start-dev.sh", "kill-port": "lsof -ti :3000 | xargs kill -9 2>/dev/null || echo '端口 3000 未被占用'", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@stagewise-plugins/react": "^0.4.8", "@stagewise/toolbar-react": "^0.4.6", "ahooks": "^3.8.5", "axios": "^1.6.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cytoscape": "^3.28.1", "cytoscape-cola": "^2.5.1", "cytoscape-cose-bilkent": "^4.1.0", "cytoscape-dagre": "^2.5.0", "cytoscape-fcose": "^2.2.0", "lucide-react": "^0.316.0", "monaco-editor": "^0.52.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "tailwind-merge": "^2.2.1", "zustand": "^4.4.7"}, "devDependencies": {"@types/cytoscape": "^3.19.16", "@types/cytoscape-fcose": "^2.2.4", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.2.2", "vite": "^5.1.0"}}