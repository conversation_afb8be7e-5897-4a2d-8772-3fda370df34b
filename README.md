# 保险关系图谱分析系统

基于 Neo4j 的关系图谱可视化 Web 应用，专为保险行业设计，用于分析隐藏的关联关系和风险传导路径。

## 技术栈

- **前端框架**: React 18 + TypeScript + Vite
- **UI 组件库**: shadcn/ui + Tailwind CSS  
- **图可视化**: Cytoscape.js + 多种布局算法
- **路由**: React Router v6
- **状态管理**: Zustand
- **HTTP 客户端**: Axios
- **图标**: Lucide React

## 功能特性

### 🎯 核心功能
- **图谱可视化**: 基于 Cytoscape.js 的交互式图谱展示
- **节点管理**: 支持人员、公司、保单、理赔等多种节点类型
- **关系分析**: 深度挖掘节点间的关联关系
- **路径发现**: 找出节点间的最短路径和关键路径
- **风险识别**: 智能识别异常关联和风险传导路径

### 🔍 分析工具
- **实时搜索**: 快速检索节点和关系
- **智能筛选**: 按类型、属性等条件筛选
- **数据导出**: 支持图谱导出为 PNG 等格式
- **批量操作**: 支持批量导入和处理数据

### 📊 可视化特性
- **多种布局**: Cola、Dagre、Circle 等多种布局算法
- **节点分类**: 不同颜色和图标区分节点类型
- **交互操作**: 缩放、拖拽、选择等丰富交互
- **详情面板**: 选中节点/关系的详细信息展示

## 项目结构

```
bzn-relational-graph-client/
├── src/
│   ├── components/          # 可复用组件
│   │   ├── graph/          # 图可视化组件
│   │   ├── layout/         # 布局组件
│   │   └── ui/             # 基础 UI 组件
│   ├── pages/              # 页面组件
│   │   ├── Dashboard.tsx   # 仪表板
│   │   ├── GraphAnalysis.tsx # 图谱分析
│   │   ├── NodeManagement.tsx # 节点管理
│   │   ├── RelationshipAnalysis.tsx # 关系分析
│   │   └── Settings.tsx    # 系统设置
│   ├── services/           # API 服务
│   │   └── api.ts         # API 接口定义
│   ├── stores/             # 状态管理
│   │   └── graphStore.ts  # 图数据状态
│   ├── lib/               # 工具函数
│   │   └── utils.ts       # 通用工具
│   ├── App.tsx            # 应用主组件
│   ├── main.tsx           # 应用入口
│   └── index.css          # 全局样式
├── public/                # 静态资源
├── package.json           # 项目配置
├── vite.config.ts         # Vite 配置
├── tailwind.config.js     # Tailwind 配置
└── tsconfig.json          # TypeScript 配置
```

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

应用将在 http://localhost:3000 启动

### 3. 构建生产版本

```bash
npm run build
```

## API 接口

**📍 服务地址**: http://127.0.0.1:8000  
**📖 API文档**: http://127.0.0.1:8000/api/v1/docs

### 主要接口

#### 图谱可视化
- `GET /api/v1/graph/overview` - 获取图概览统计
- `GET /api/v1/graph/sample/nodes` - 获取节点样本
- `GET /api/v1/graph/search/nodes` - 搜索节点
- `GET /api/v1/graph/nodes/{id}/neighbors` - 获取节点邻居
- `POST /api/v1/graph/cypher/execute` - 执行 Cypher 查询

#### 节点管理
- `GET /api/v1/nodes/persons/{type}/` - 人员节点 (agents/bd/mo/insured)
- `GET /api/v1/nodes/organizations/{type}/` - 组织节点
- `GET /api/v1/nodes/policies/` - 保单节点
- `GET /api/v1/nodes/areas/` - 区域节点

#### 系统监控
- `GET /health` - 健康检查
- `GET /api/v1/nodes/{type}/stats` - 节点统计信息

## 界面展示

### 仪表板
- 总体数据统计
- 最近活动动态
- 关键指标监控

### 图谱分析
- 交互式图谱可视化
- 实时搜索和筛选
- 节点/关系详情展示

### 节点管理
- 节点列表管理
- 批量操作支持
- 详细信息编辑

## 部署说明

### 开发环境
1. **确保后端服务运行**: http://127.0.0.1:8000
2. **安装依赖**: `npm install`
3. **启动开发服务器**: `npm run dev`
4. **访问应用**: http://localhost:3000

### 生产环境
1. **构建项目**: `npm run build`
2. **部署静态文件**: 部署 `dist` 目录到 Web 服务器
3. **配置代理**: 确保前端能访问后端 API 服务

### 环境检查
```bash
# 检查后端服务
curl http://127.0.0.1:8000/health

# 检查前端服务
curl http://localhost:3000
```

## 浏览器支持

- Chrome >= 88
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 技术支持

如有问题或建议，请通过以下方式联系：

- 邮箱: <EMAIL>
- 文档: [项目文档](https://docs.example.com)
- Issues: [GitHub Issues](https://github.com/example/bzn-relational-graph-client/issues) 